package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.SalesManagerProductDto;

import java.util.List;

@Validated
@Tag(name = "sales-manager-controller", description = "API для взаимодействия с заявками на покупку")
@RequestMapping("/api/v1/sales-manager")
public interface SalesManagerApiDelegate {
    @Operation(
            summary = "Получить список товаров на покупку",
            description = "Получить список товаров на покупку",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Список товаров на покупку"),
                    @ApiResponse(responseCode = "400", description = "Некорректные параметры запроса")
            }
    )
    @GetMapping(
            value = "/sales-orders",
            produces = "application/json"
    )
    default ResponseEntity<List<SalesManagerProductDto>> getSalesOrders(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @Parameter(name = "shipmentId", description = "ID товара для заявки", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "shipmentId") Long shipmentId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
