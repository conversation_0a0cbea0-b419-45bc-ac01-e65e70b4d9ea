package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;
import ru.oskelly.concierge.service.StateTransitionHistoryService;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class StateHistoryApiController implements StateHistoryApiDelegate {
    private final StateTransitionHistoryService stateTransitionHistoryService;
    @Override
    public ResponseEntity<List<PurchaseOrderStateHistoryDTO>> getStateTransitionHistory(Long orderId) {
        return ResponseEntity.ok(stateTransitionHistoryService.getTransitionHistory(orderId));
    }
}
