package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;

import java.util.List;

@Validated
@Tag(name = "concierge-state-history-controller", description = "API для получения истории изменения состояний заявок")
@RequestMapping("/api/v1/purchase-order")
public interface StateHistoryApiDelegate {

    @Operation(
            operationId = "getStateTransitionHistory",
            summary = "Получение истории изменения состояний",
            description = "Получение истории изменения состояний заявки с указанием автора и времени изменения",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "История изменения состояний получена",
                            content = @Content(
                                    mediaType = "application/json",
                                    array = @ArraySchema(schema = @Schema(implementation = PurchaseOrderStateHistoryDTO.class))
                            )),
                    @ApiResponse(responseCode = "404", description = "Не найдена заявка по указанному ID"),
                    @ApiResponse(responseCode = "500", description = "Внутренняя ошибка")
            }
    )
    @GetMapping(
            value = "/{orderId}/history",
            produces = {"application/json"}
    )
    default ResponseEntity<List<PurchaseOrderStateHistoryDTO>> getStateTransitionHistory(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
