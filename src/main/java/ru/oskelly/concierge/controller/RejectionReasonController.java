package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.RejectionReason;
import ru.oskelly.concierge.data.model.enums.ObjectType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.exception.RejectionReasonException;
import ru.oskelly.concierge.service.RejectionReasonService;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class RejectionReasonController implements RejectionReasonApiDelegate {
    private final RejectionReasonService rejectionReasonService;

    @Override
    public ResponseEntity<List<RejectionReason>> getReasons(Long userId, ObjectType objectType, PurchaseOrderStatusEnum status) {
        if (objectType != ObjectType.PURCHASE_ORDER && status != null) {
            throw new RejectionReasonException("Статус разрешен только для типа объекта ORDER", null, null, 400);
        }
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok()
                .body(rejectionReasonService.getRejectionReasons(userId, objectType, status));
    }
}
