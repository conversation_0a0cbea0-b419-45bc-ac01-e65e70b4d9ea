package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;

@Tag(
    name = "manual-purchase-order-assignment-controller",
    description = "API ручного назначения сотрудников на заявки"
)
@Validated
@RequestMapping("/api/v1/purchase/order/{orderId}")
public interface ManualPurchaseOrderAssignmentApiDelegate {

    @Operation(
        operationId = "assignOrderToSales",
        summary = "Назначить Сейлза на заявку",
        description = "Ручная инициализация процесса назначения Сейлза на заявку",
        responses = @ApiResponse(
            responseCode = "200",
            description = "Процесс успешно запущен"
        )
    )
    @PostMapping(
        value = "/assign/sales",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<Void> assignOrderToSales(
        @Parameter(
            name = "orderId",
            description = "ID заявки",
            required = true,
            in = ParameterIn.PATH
        )
        @PathVariable(name = "orderId") Long orderId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
        operationId = "assignOrderToAdminSales",
        summary = "Назначить Админа на заявку Сейлза",
        description = "Назначение Админа (сейлза или бутиков) на заявку Сейлза",
        responses = @ApiResponse(
            responseCode = "200",
            description = "Админ назначен на заявку"
        )
    )
    @PostMapping(
        value = "/assign/admin/sales",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<Void> assignOrderToAdminSales(
        @Parameter(
            name = "orderId",
            description = "ID заявки",
            required = true,
            in = ParameterIn.PATH
        )
        @PathVariable(name = "orderId") Long orderId,

        @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Данные Админа для назначения заявки",
            required = true,
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = SalesInfoDTO.class)
            )
        )
        @RequestBody SalesInfoDTO salesInfo
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
        operationId = "assignOrderToSourcer",
        summary = "Назначить Сорсера на заявку",
        description = "Ручная инициализация процесса назначения Сорсера на заявку",
        responses = @ApiResponse(
            responseCode = "200",
            description = "Процесс успешно запущен"
        )
    )
    @PostMapping(
        value = "/assign/sourcer",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<Void> assignOrderToSourcer(
        @Parameter(
            name = "orderId",
            description = "ID заявки",
            required = true,
            in = ParameterIn.PATH
        )
        @PathVariable(name = "orderId") Long orderId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
        operationId = "assignOrderToAdminSourcer",
        summary = "Назначить Админа на заявку Сорсера",
        description = "Назначение Админа Сорсеров на заявку Сорсера",
        responses = @ApiResponse(
            responseCode = "200",
            description = "Админ назначен на заявку"
        )
    )
    @PostMapping(
        value = "/assign/admin/sourcer",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    default ResponseEntity<Void> assignOrderToAdminSourcer(
        @Parameter(
            name = "orderId",
            description = "ID заявки",
            required = true,
            in = ParameterIn.PATH
        )
        @PathVariable(name = "orderId") Long orderId,

        @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Данные Админа для назначения заявки",
            required = true,
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = SourcerInfoDTO.class)
            )
        )
        @RequestBody SourcerInfoDTO sourcerInfo
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
