package ru.oskelly.concierge.controller.admin;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.SendOffersToClientRequest;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;
import ru.oskelly.concierge.service.OfferService;

@RestController
@RequiredArgsConstructor
public class OfferController implements OfferApiDelegate {
    private final OfferService offerService;
    @Override
    public ResponseEntity<ShipmentOffersDTO> addingProducts(Long userId, ShipmentOffersDTO shipmentOffers) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(offerService.addingProducts(shipmentOffers));
    }

    @Override
    public ResponseEntity<ShipmentOffersDTO> addingShoppers(Long userId, ShipmentOffersDTO shipmentOffers) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(offerService.addingShoppers(shipmentOffers));
    }

    @Override
    public ResponseEntity<Void> deleteProduct(Long userId, Long offerId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(offerService.deleteProduct(offerId));
    }

    @Override
    public ResponseEntity<ShipmentOffersDTO> addShopperOffer(Long userId, ShipmentOffersDTO shipmentOffers) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(offerService.addShopperOffer(shipmentOffers));
    }

    @Override
    public ResponseEntity<Void> sendOffers(Long userId, SendOffersToClientRequest request) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        offerService.sendOffersToClient(request);

        return ResponseEntity.ok().build();
    }
}
