package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperCreateRequest;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.service.PersonalShopperService;

@RestController
@RequiredArgsConstructor
public class PersonalShopperController implements PersonalShopperApiDelegate {

    private final PersonalShopperService personalShopperService;

    @Override
    public ResponseEntity<PersonalShopper> createPersonalShopper(PersonalShopperCreateRequest request) {
        return ResponseEntity.ok(personalShopperService.createPersonalShopper(request));
    }

    @Override
    public ResponseEntity<PersonalShopperFilter> filterPersonalShopper() {
        return ResponseEntity.ok(personalShopperService.filterPersonalShopper());
    }

    @Override
    public ResponseEntity<PaginatedShoppersResult> getPersonalShoppers(PersonalShopperFilter filter, Long userId, String searchText) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(personalShopperService.getPersonalShoppers(filter, searchText));
    }
}
