package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Set;

/**
 * DTO для передачи информации о заказе для обработки.
 */
@Schema(description = "Информация о заказе для обработки")
public record OrderInfoDTO(
        @Schema(description = "Идентификатор продукта")
        @ArraySchema(schema = @Schema(implementation = Long.class))
        Set<Long> productIds,
        
        @Schema(description = "Идентификатор заказа", example = "456")
        Long orderID,
        
        @Schema(description = "Идентификатор клиента", example = "789")
        Long clientId
) {}