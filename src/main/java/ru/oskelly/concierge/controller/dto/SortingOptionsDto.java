package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.util.List;

@Schema(description = "DTO for sorting options")
@Builder
public record SortingOptionsDto(
        @ArraySchema(
                schema = @Schema(
                        description = "List of available sorting options",
                        requiredMode = Schema.RequiredMode.REQUIRED, implementation = SortingOption.class
                )
        )
        List<SortingOption> sorting
) {
}
