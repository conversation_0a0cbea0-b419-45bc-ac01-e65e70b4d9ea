package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.data.model.enums.AccessSource;
import ru.oskelly.concierge.data.model.enums.InteractionType;
import ru.oskelly.concierge.data.model.enums.PaymentFormat;
import ru.oskelly.concierge.data.model.enums.ShopperCategoryEnum;

import java.time.ZonedDateTime;
import java.util.Set;

@Schema(description = "Данные для создания PersonalShopper")
public record PersonalShopperCreateRequest(
        @Schema(description = "Идентификатор пользователя", example = "12345")
        @NotNull(message = "user_id обязателен")
        Long userId,

        @Schema(description = "Никнейм пользователя", example = "shoper_ivan")
        String nickname,

        @Schema(description = "Email пользователя", example = "<EMAIL>")
        String email,

        @Schema(description = "Полное имя пользователя", example = "Иван Иванов")
        String name,

        @Schema(description = "Тип взаимодействия", example = "[INTERNET_SITES, MULTIBRAND, ON_REQUEST, UNIVERSAL]")
        @NotNull(message = "interaction_type обязателен")
        @ArraySchema(arraySchema = @Schema(implementation = InteractionType.class))
        Set<InteractionType> interactionType,

        @Schema(description = "Формат оплаты", allowableValues = {"PREPAYMENT", "POSTPAYMENT"})
        @NotNull(message = "payment_format обязателен")
        PaymentFormat paymentFormat,

        @Schema(description = "Список категорий байера", example = "[CLOTHING, FOOTWEAR]")
        @ArraySchema(arraySchema = @Schema(implementation = ShopperCategoryEnum.class))
        Set<ShopperCategoryEnum> categories,

        @Schema(description = "Список брендов", example = "[1, 2, 3]")
        @ArraySchema(arraySchema = @Schema(implementation = Long.class))
        Set<Long> brands,

        @Schema(description = "Источник доступа", implementation = AccessSource.class)
        @NotNull(message = "access_source обязателен")
        AccessSource accessSource,

        @Schema(description = "Приоритетный статус", example = "true")
        @NotNull(message = "priority обязателен")
        boolean priority,

        @Schema(description = "Дата присвоения статуса персонального шоппера", example = "2023-01-01T12:00:00Z")
        ZonedDateTime dateShopperStatus
) {}