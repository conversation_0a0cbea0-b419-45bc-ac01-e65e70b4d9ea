package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

@Builder
@Schema(description = "DTO для добавления комментария к товару в заявке")
public record ShipmentCommentDTO(

        @Schema(description = "ID товара", example = "123")
        Long shipmentId,

        @Schema(description = "Комментарий к товару", example = "Маленькая, бежевая")
        String comment
) {
}
