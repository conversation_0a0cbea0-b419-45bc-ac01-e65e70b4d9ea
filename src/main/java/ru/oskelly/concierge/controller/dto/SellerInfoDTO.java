package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * DTO с информацией о продавце
 */
@Schema(description = "Информация о продавце/шопера товара")
public record SellerInfoDTO(
        @Schema(
                description = "Уникальный идентификатор продавца/шопера в системе",
                example = "12345",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @NotNull
        Long sellerId,
        @Schema(
                description = "Тип продавца",
                implementation = DescriptionStructureEnum.class
        )
        DescriptionStructureEnum sellerType,

        @Schema(
                description = "ФИО продавца/шопера",
                example = "Иванов Иван Иванович",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED,
                maxLength = 100
        )
        String sellerFio,

        @Schema(
                description = "Электронная почта продавца/шопера",
                example = "<EMAIL>",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED,
                format = "email",
                maxLength = 50
        )
        String sellerEmail,

        @Schema(
                description = "Никнейм продавца/шопера в системе",
                example = "best_seller_2023",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED,
                maxLength = 30
        )
        String sellerNickname,
        @Schema(
                description = "Ссылка на аватар продавца/шопера",
                example = "https://example.com/avatar.jpg",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED,
                maxLength = 200
        )
        String urlAvatar
) {
}