package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import ru.oskelly.concierge.data.model.enums.TypesSorting;

@Schema(description = "Single sorting option")
@Builder
public record SortingOption(
        @Schema(
                description = "Sorting code (enum value)",
                requiredMode = Schema.RequiredMode.REQUIRED,
                implementation = TypesSorting.class
        )
        TypesSorting code,

        @Schema(
                description = "Human-readable sorting name",
                example = "Сначала новые",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        String description,

        @Schema(
                description = "Flag indicating if this option is currently selected",
                example = "true",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        boolean isSelected

) {
    public SortingOption(TypesSorting code, boolean isSelected) {
        this(code, code.getDescription(), isSelected);
    }
}
