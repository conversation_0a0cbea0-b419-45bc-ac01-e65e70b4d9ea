package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.bind.DefaultValue;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

import java.util.List;

/**
 * Запрос на создание заявки
 */
@Schema(description = "DTO для создания новой заявки на покупку")
public record PurchaseOrderCreateRequest(
        @Schema(description = "Информация о покупателе", requiredMode = Schema.RequiredMode.REQUIRED)
        CustomerInfoDTO customerInfo,

        @Schema(description = "Информация о менеджере подбора предложения", requiredMode = Schema.RequiredMode.REQUIRED)
        SourcerInfoDTO sourcerInfo,

        @Schema(description = "Информация о менеджере по продажам", requiredMode = Schema.RequiredMode.REQUIRED)
        SalesInfoDTO salesInfo,

        @Schema(description = "Источник создания заявки", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "Источник заявки обязателен для заполнения")
        PurchaseOrderSourceEnum source,

        @Schema(description = "Описание заявки", example = "Необходимо закупить офисную технику")
        String description,

        @Schema(description = "Ссылки на изображения")
        List<String> imagesUrl,

        @Schema(description = "Признак создания заявки в статусе NEW", example = "true", defaultValue = "true")
        @DefaultValue("true")
        boolean purchaseToNew,

        @Schema(description = "Ссылка на товар")
        String link) {
}
