package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.data.model.enums.TypeSeller;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Schema(description = "DTO товара для сейлз менеджера")
public record SalesManagerProductDto(
        @Schema(description = "Уникальный идентификатор товара c платформы", example = "1")
        Long productId,

        @Schema(description = "DTO с информацией о продавца(шопер)", implementation = SellerInfoDTO.class)
        SellerInfoDTO seller,

        @Schema(description = "Тип товара", implementation = TypeSeller.class)
        TypeSeller type,

        @Schema(description = "Цена товара", example = "330000")
        BigDecimal price,

        @Schema(description = "Дата доставки")
        ZonedDateTime deliveryDate,

        @Schema(description = "Действителен до")
        ZonedDateTime validUntil,

        @Schema(description = "Лучшая цена")
        boolean bestPrice,

        @Schema(description = "Быстрая доставка")
        boolean fastDelivery
) {
}
