package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.util.List;

/**
 * DTO для создания или обновления товара в заявке
 */
@Builder
@Schema(description = "DTO для создания или обновления товара в заявке")
public record ShipmentRequestDTO(
        @Schema(description = "ID категории товара", example = "123")
        Long categoryId,

        @Schema(description = "Наименование категории товара")
        String categoryName,

        @Schema(description = "ID создателя товара", example = "123")
        Long creatorId,

        @Schema(description = "ID бренда товара", example = "456")
        Long brandId,

        @Schema(description = "Наименование бренда товара")
        String brandName,

        @Schema(description = "Наименование бренда товара (транслитерация)")
        String brandTransliterateName,

        @Schema(description = "ID атрибута материала товара", example = "789")
        Long materialAttributeId,

        @Schema(description = "Наименование атрибута материала товара", example = "Синтетика")
        String materialAttributeName,

        @Schema(description = "ID атрибута цвета товара", example = "101")
        Long colorAttributeId,

        @Schema(description = "Наименование цвета товара")
        String colorAttributeName,

        @Schema(description = "ID модели товара", example = "202")
        Long modelId,

        @Schema(description = "Наименование модели товара")
        String modelName,

        @Schema(description = "Размеры товара")
        ShimpentSizeDTO shipmentSize,

        @Schema(description = "Описание товара", example = "Кожаная куртка черного цвета")
        String description,

        @Schema(description = "Список изображений товара")
        @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
        List<ImageDTO> images,

        @Schema(description = "Ссылки на товар", example = "[\"https://example.com/product1\"]")
        @ArraySchema(schema = @Schema(implementation = String.class))
        List<String> links,

        @Schema(description = "Комментарий к товару", example = "Маленькая, бежевая")
        String comment
) {
}
