package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

/**
 * ДТО с информацией о состоянии товара в предложении
 */
@Schema(description = "Описание состояния товара")
public record ProductConditionDTO(
        @NotNull
        @Schema(description = "ID состояния товара в монолите",
                example = "1",
                requiredMode = Schema.RequiredMode.REQUIRED)
        Long id,

        @Schema(description = "Название состояния товара",
                example = "NEW")
        String name,

        @Schema(description = "Описание состояния товара",
                example ="Новое с биркой")
        String description
) {
}
