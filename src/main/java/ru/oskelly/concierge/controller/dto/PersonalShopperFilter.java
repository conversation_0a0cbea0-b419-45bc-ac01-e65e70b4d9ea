package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Set;

/**
 * Фильтр для поиска персональных шоперов
 */
@Schema(description = "Фильтр для подбора персональных шоперов")
public record PersonalShopperFilter(
        @Schema(
                description = "Список источников доступа",
                type = "array"
        )
        @ArraySchema(schema = @Schema(implementation = DescriptionStructureEnum.class))
        List<DescriptionStructureEnum> accessSources,

        @Schema(
                description = "Список категорий",
                type = "array"
        )
        @ArraySchema(schema = @Schema(implementation = DescriptionStructureEnum.class))
        List<DescriptionStructureEnum> categories,

        @ArraySchema(
                arraySchema = @Schema(
                        description = "Идентификаторы брендов для фильтрации",
                        requiredMode = Schema.RequiredMode.NOT_REQUIRED
                ),
                schema = @Schema(
                        description = "ID бренда",
                        example = "456",
                        minimum = "1"
                )
        )
        Set<Long> brandIds,

        @Schema(
                description = "Номер страницы (начиная с 0)",
                example = "0"
        )
        Integer page,

        @Schema(
                description = "Размер страницы (количество элементов на странице)",
                example = "20"
        )
        Integer pageSize
) {
}
