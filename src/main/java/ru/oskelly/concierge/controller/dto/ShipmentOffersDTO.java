package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Builder;

import java.util.List;

/**
 * DTO для передачи списка предложений, связанных с отправкой.
 */
@Schema(description = "Список предложений для отправки")
@Builder
public record ShipmentOffersDTO(
        @Schema(description = "ID товара в заявке")
        Long shipmentId,
        @Schema(description = "Список предложений продавцов")
        @ArraySchema(arraySchema = @Schema(implementation = OfferDTO.class))
        List<@Valid OfferDTO> offers
) { }