package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Результат пагинации списка PersonalShopper
 * @param items - список PersonalShopper
 * @param itemsCount - общее количество PersonalShopper
 */
public record PaginatedShoppersResult(
        @Schema(description = "Список элементов PersonalShopper для текущей страницы")
        @ArraySchema(schema = @Schema(implementation = PersonalShopperDTO.class))
        List<PersonalShopperDTO> items,
        @Schema(
                description = "Общее количество элементов, доступных по всем страницам (соответствующих критериям фильтрации)",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "153",
                type = "integer",
                format = "int64"
        )
        long itemsCount,
        @Schema(
                description = "Общее количество страниц всех элементов, соответствующих критериям фильтрации",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "10",
                type = "integer",
                format = "int64"
        )
        long totalPages,
        @Schema(
                description = "Общее количество элементов, доступных по всем страницам (соответствующих критериям фильтрации)",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "153",
                type = "integer",
                format = "int64"
        )
        long totalAmount
) {
}
