package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

import java.util.Map;

/**
 * Запись, представляющая доступные источники заказов с их описаниями.
 */
@Schema(description = "Доступные источники заказов с их описаниями")
@Builder
public record OrderSources(
        @Schema(description = "Карта перечислений источников заказов с их человекочитаемыми описаниями",
                example = "{\"ONLINE\": \"Онлайн заказ\", \"PHONE\": \"Заказ по телефону\"}")
        Map<PurchaseOrderSourceEnum, String> sources
) { }
