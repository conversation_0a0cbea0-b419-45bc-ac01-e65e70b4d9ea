package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * DTO с информацией о продавце/менеджере по продажам
 * <p>
 * Содержит идентификационные данные сотрудника, отвечающего за продажи.
 */
@Schema(description = "DTO с информацией о продавце/менеджере по продажам")
public record SalesInfoDTO(
        @Schema(
                description = "Уникальный идентификатор продавца в системе",
                example = "789",
                format = "int64"
        )
        Long salesId,

        @Schema(
                description = "Фамилия, имя и отчество продавца",
                example = "Петрова Анна Сергеевна",
                maxLength = 255
        )
        String fio,

        @Schema(
                description = "Никнейм продавца",
                example = "petrova",
                maxLength = 255
        )
        String nickName,

        @Schema(
                description = "Ссылка на картинку аватара",
                example = "https://example.com/avatar.jpg")
        String urlAvatar,

        @Schema(
            description = "Роль продавца",
            example = "SALES"
        )
        Roles salesRole
) {
}
