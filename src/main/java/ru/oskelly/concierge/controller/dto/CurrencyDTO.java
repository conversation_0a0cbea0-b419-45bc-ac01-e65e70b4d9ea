package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO для представления валюты
 */
@Schema(description = "DTO для представления валюты")
public record CurrencyDTO(
        @Schema(description = "Уникальный идентификатор валюты", example = "1")
        Long id,

        @Schema(description = "Полное наименование валюты", example = "Российский рубль")
        String name,

        @Schema(description = "Символ валюты", example = "₽")
        String sign,

        @Schema(description = "Буквенный код валюты по стандарту ISO 4217", example = "RUB")
        String isoCode,

        @Schema(description = "Числовой код валюты по стандарту ISO 4217", example = "643")
        Integer isoNumber,

        @Schema(description = "Признак того, что валюта является базовой для системы (например, для конвертации)", example = "true")
        boolean isBase,

        @Schema(description = "Признак того, что валюта доступна для использования в системе", example = "true")
        boolean isActive,

        @Schema(description = "Признак того, что валюта выбрана по умолчанию для отображения пользователю", example = "true")
        boolean selectedByDefault
) {
}
