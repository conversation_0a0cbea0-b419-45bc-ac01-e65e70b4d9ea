package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Builder;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * DTO для фильтрации заявок
 */
@Schema(description = "DTO для фильтрации заявок")
@Builder
public record PurchaseOrderFilter(
        @Schema(
                description = "Список группировок статусов с количеством",
                type = "array"
        )
        @ArraySchema(schema = @Schema(implementation = GroupingStatusQuantity.class))
        List<GroupingStatusQuantity> groupingStatusQuantities,
        @Schema(
                description = "Список группировок источников с количеством",
                type = "array"
        )
        @ArraySchema(schema = @Schema(implementation = GroupingSourceQuantity.class))
        List<GroupingSourceQuantity> groupingSourceQuantities,
        @Schema(
                description = "Список идентификаторов брендов для фильтрации",
                example = "[1, 2, 3]"
        )
        List<Long> brands,
        @Schema(
                description = "Список идентификаторов моделей для фильтрации",
                example = "[101, 102, 103]"
        )
        List<Long> models,
        @Schema(
                description = "Начальная дата для фильтрации (включительно)",
                example = "2023-01-01T00:00:00+03:00",
                format = "date-time", requiredMode = Schema.RequiredMode.REQUIRED
        )
        ZonedDateTime fromDate,

        @Schema(
                description = "Конечная дата для фильтрации (включительно)",
                example = "2023-12-31T23:59:59+03:00",
                format = "date-time", requiredMode = Schema.RequiredMode.REQUIRED
        )
        ZonedDateTime toDate,

        @Schema(
                description = "Номер страницы для пагинации (начинается с 0)",
                example = "1",
                defaultValue = "1",
                minimum = "1"
        )
        @Min(value = 1, message = "Номер страницы не может быть меньше 1")
        Long page,

        @Schema(
                description = "Размер страницы для пагинации",
                example = "20",
                defaultValue = "20",
                minimum = "1"
        )
        @Min(value = 1, message = "Размер страницы не может быть меньше 1")
        Long pageSize,

        @Schema(
                description = "Тип сортировки",
                implementation = SortingOptionsDto.class
        )
        SortingOptionsDto typesSorting
) {
}
