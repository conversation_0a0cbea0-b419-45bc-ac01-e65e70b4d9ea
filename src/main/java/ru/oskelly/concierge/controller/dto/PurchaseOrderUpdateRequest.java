package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;

import java.util.List;

/**
 * DTO для обновления данных заявки на покупку
 */
@Schema(description = "Запрос на обновление данных заявки на покупку")
public record PurchaseOrderUpdateRequest(
        @Schema(
                description = "Информация о клиенте"
        )
        CustomerInfoDTO customerInfo,

        @Schema(
                description = "Информация о менеджере по подбору предложений"
        )
        SourcerInfoDTO sourcerInfo,

        @Schema(
                description = "Информация о менеджере по продажам"
        )
        SalesInfoDTO salesInfo,

        @Schema(
                description = "Детальное описание заявки",
                example = "Нужен ноутбук для офисной работы с диагональю 15 дюймов",
                maxLength = 1000,
                requiredMode = Schema.RequiredMode.NOT_REQUIRED
        )
        String description,

        @Schema(
                description = "Ссылки на изображения, относящиеся к заявке",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @ArraySchema(schema = @Schema(implementation = String.class))
        List<String> imagesUrl,

        @Schema(
                description = "Ссылка на товар",
                example = "www.jacquemus.com"
        )
        String link
) {
}
