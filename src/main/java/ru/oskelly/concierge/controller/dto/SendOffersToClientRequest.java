package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import java.util.List;

/**
 * Запрос на отправку предложений клиенту в WhatsApp
 */
public record SendOffersToClientRequest(
    @NotNull(message = "Идентификатор заявки обязателен для заполнения")
    @Schema(description = "ID заявки")
    Long orderId,

    @Schema(description = "ID предложений")
    @ArraySchema(arraySchema = @Schema(implementation = Long.class))
    List<Long> offerIds,

    @Schema(description = "ID предложений от байеров")
    @ArraySchema(arraySchema = @Schema(implementation = Long.class))
    List<Long> proposedOfferIds,

    @Pattern(regexp = ".*\\D.*", message = "Текст сообщения не должен содержать только цифры")
    @NotBlank(message = "Текст сообщения обязателен для заполнения")
    @Schema(type = "string", description = "Текст сообщения")
    String message
) {
}
