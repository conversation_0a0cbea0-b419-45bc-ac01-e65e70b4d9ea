package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Обертка для результатов запроса с пагинацией.
 * Содержит данные для текущей страницы и общее количество записей.
 *
 * @param data       Список элементов данных для текущей страницы.
 * @param totalCount Общее количество элементов, доступных по всем страницам,
 *                   соответствующих критериям фильтрации (без учета пагинации).
 */
@Schema(description = "Результат запроса с пагинацией")
public record PaginatedResult(
        @Schema(
                description = "Список элементов данных для текущей страницы"
        )
        @ArraySchema(schema = @Schema(implementation = OrdersForConciergeDTO.class))
        List<OrdersForConciergeDTO> data,

        @Schema(
                description = "Общее количество страниц всех элементов, соответствующих критериям фильтрации",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "10",
                type = "integer",
                format = "int64"
        )
        long totalPages,

        @Schema(
                description = "Общее количество элементов, доступных по всем страницам (соответствующих критериям фильтрации)",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "153",
                type = "integer",
                format = "int64"
        )
        long totalCount
) {
}
