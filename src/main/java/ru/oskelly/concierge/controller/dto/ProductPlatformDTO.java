package ru.oskelly.concierge.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * DTO для представления платформы продукта
 */
@Schema(description = "Продукт с платформы")
@JsonInclude(JsonInclude.Include.NON_NULL)
public record ProductPlatformDTO(
        @Schema(description = "Идентификатор товара, существующего на платформе", example = "1")
        Long productId,

        @Schema(description = "Цена в валюте", example = "200.00")
        BigDecimal currencyPrice,

        @Schema(description = "Фото товара", implementation = ImageDTO.class)
        ImageDTO productPhoto,

        @Schema(description = "Расположение товара", example = "Москва, склад №1")
        String productLocation,

        @Schema(description = "Бренд", example = "Nike")
        String brand,

        @Schema(description = "Категория товара", example = "Обувь")
        String productCategory,

        @Schema(description = "Размеры в наличии", example = "[\"40\", \"41\", \"42\"]")
        List<String> availableSizes,

        @Schema(description = "Цена без скидки", example = "250.00")
        BigDecimal priceWithoutDiscount,

        @Schema(description = "Цена со скидкой (при наличии)", example = "200.00")
        BigDecimal priceWithDiscount,

        @Schema(description = "Размер скидки (при наличии)", example = "50.00")
        BigDecimal discountAmount,

        @Schema(description = "Тип размера", example = "INT")
        String sizeType,

        @Schema(description = "Идентификатор состояния товара", example = "1")
        Integer conditionId,

        @Schema(description = "Название состояния товара", example = "Новое с биркой")
        String conditionName,

        @Schema(description = "Состояние публикации товара", example = "PUBLISHED")
        String productState,

        @Schema(description = "Количество лайков", example = "0")
        Integer likesCount,

        @Schema(description = "Флаг, указывающий, лайкнут ли товар пользователем", example = "false")
        Boolean isLiked,

        @Schema(description = "URL товара", example = "/products/shorty-fear-of-god-essentials-new-3013660")
        String url,

        @Schema(description = "Размер скидки в процентах")
        BigDecimal discount,

        @Schema(description = "Информация о валюте", implementation = CurrencyDTO.class)
        CurrencyDTO currency
) {
}
