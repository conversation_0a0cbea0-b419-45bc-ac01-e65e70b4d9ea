package ru.oskelly.concierge.controller.dto;

import lombok.Builder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

/**
 * DTO для группировки по статусу и количеству
 *
 * @param statusId    - статус
 * @param quantity    - количество заявок
 * @param description - описание статуса
 */
@Builder
public record GroupingStatusQuantity(
        PurchaseOrderStatusEnum statusId,
        Long quantity,
        String description
) {
    public GroupingStatusQuantity(PurchaseOrderStatusEnum statusId, Long quantity) {
        this(statusId, quantity, statusId.getDescription());
    }
}
