package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DTO для создания события отклонения
 */
@Schema(description = "DTO для создания события отклонения")
@Data
public class RejectionEventRequest {

    /**
     * Идентификатор отклоняемого объекта
     */
    @Schema(
            description = "Идентификатор отклоняемого объекта",
            example = "12345",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Long objectId;

    /**
     * Причина отклонения
     */
    @Schema(
            description = "Причина отклонения",
            example = "Не соответствует требованиям",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String rejectionReason;

    /**
     * Дополнительное описание
     */
    @Schema(
            description = "Дополнительное описание",
            example = "Отсутствуют необходимые документы"
    )
    private String additionalText;
}