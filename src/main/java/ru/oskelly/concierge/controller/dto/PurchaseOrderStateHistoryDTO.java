package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.time.ZonedDateTime;

/**
 * DTO для истории переходов статусов заявки
 */
@Schema(description = "DTO для истории переходов статусов заявки")
public record PurchaseOrderStateHistoryDTO(
        @Schema(description = "Идентификатор истории", example = "123")
        Long id,

        @Schema(description = "Идентификатор заявки", example = "456")
        Long purchaseOrderId,

        @Schema(description = "Идентификатор пользователя", example = "789")
        Long userId,

        @Schema(description = "Ник пользователя", example = "user123")
        String userNickName,

        @Schema(description = "Статус источника", example = "CREATED")
        PurchaseOrderStatusEnum sourceState,

        @Schema(description = "Статус цели", example = "APPROVED")
        PurchaseOrderStatusEnum targetState,

        @Schema(description = "Дата перехода", example = "2024-05-20T12:00:00+03:00")
        ZonedDateTime transitionDate,

        @Schema(description = "Комментарий", example = "Дополнительные сведения")
        String comment,

        @Schema(description = "Причина возврата", example = "Нет в наличии")
        String reasonReturn
) {}
