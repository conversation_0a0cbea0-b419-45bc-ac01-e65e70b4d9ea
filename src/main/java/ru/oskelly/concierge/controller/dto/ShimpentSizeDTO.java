package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Set;

/**
 * DTO для представления размера отправки
 */
@Schema(description = "DTO для представления размера")
public record ShimpentSizeDTO(
        @Schema(description = "Тип размера", example = "Large")
        String type,

        @Schema(description = "Идентификатор размера", example = "123")
        Long sizeId,

        @Schema(description = "Доступные размеры")
        @ArraySchema(schema = @Schema(implementation = String.class))
        Set<String> availableSizes) {
}
