package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.ZonedDateTime;
import java.util.UUID;

@Schema(description = "Информация об изображении, прикрепленном к заявке")
public record ImageDTO(
        @Schema(description = "Уникальный идентификатор изображения",
                example = "3fa85f64-5717-4562-b3fc-2c963f66afa6")
        UUID id,

        @Schema(description = "URL для доступа к изображению",
                example = "https://example.com/images/photo123.jpg")
        String url,

        @Schema(description = "Дата и время загрузки изображения",
                example = "2023-07-15T10:30:45Z")
        ZonedDateTime creationDate) {
}