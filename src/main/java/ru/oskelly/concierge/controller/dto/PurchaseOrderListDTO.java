package ru.oskelly.concierge.controller.dto;

import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.time.ZonedDateTime;
import java.util.List;

public record PurchaseOrderListDTO(Long id,
                                   Long customerId,
                                   PurchaseOrderSourceEnum source,
                                   ZonedDateTime creationDate,
                                   PurchaseOrderStatusEnum status,
                                   List<Long> orders) {}
