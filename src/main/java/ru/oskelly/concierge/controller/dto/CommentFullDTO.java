package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * Полное DTO комментария
 */
@Schema(description = "Полная информация о комментарии к заказу")
public record CommentFullDTO(
        @Schema(description = "Уникальный идентификатор комментария",
                example = "12345")
        Long id,

        @Schema(description = "ID автора комментария",
                example = "67890")
        Long authorId,

        @Schema(description = "Признак закрепления комментария (true - закреплен, false - не закреплен)",
                example = "false")
        Boolean isPined,

        @Schema(description = "Текст сообщения комментария",
                example = "Необходимо уточнить параметры заказа у клиента")
        String message,

        @Schema(description = "Дата и время создания комментария",
                example = "2023-07-15T14:30:00Z")
        ZonedDateTime datetime
) implements Serializable {}
