package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;

import java.util.Set;

/**
 * DTO с информацией о персональном шопере
 */
@Schema(description = "Информация о персональном шопере")
public record PersonalShopperDTO(
        @Schema(
                description = "Уникальный идентификатор шопера",
                example = "12345",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @NotNull
        Long id,
        @Schema(
                description = "Уникальный идентификатор пользователя",
                example = "12345",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        Long userId,

        @Schema(
                description = "Аватар шопера",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        String urlAvatar,

        @Schema(
                description = "ФИО шопера",
                example = "Иванова Анна Сергеевна",
                requiredMode = Schema.RequiredMode.REQUIRED,
                maxLength = 100
        )
        String fio,

        @Schema(
                description = "Никнейм шопера в системе",
                example = "super_shopper_22",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED,
                maxLength = 30
        )
        String nickname,

        @Schema(
                description = "Предпочитаемый формат оплаты",
                implementation = DescriptionStructureEnum.class,
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        DescriptionStructureEnum paymentFormat,

        @Schema(
                description = "Тип взаимодействия с клиентом",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @ArraySchema(arraySchema = @Schema(implementation = DescriptionStructureEnum.class))
        Set<DescriptionStructureEnum> interactionType,

        @Schema(
                description = "Количество завершенных заявок (со статусом DONE)",
                example = "42",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PositiveOrZero
        Long countDoneOrders
) {
}
