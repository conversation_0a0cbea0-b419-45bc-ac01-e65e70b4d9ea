package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO для передачи пары [bitrixId, orderId] из предложений.
 */
@Schema(description = "Пара bitrixId и orderId из предложения")
public record OfferPairDTO(
        @Schema(description = "Идентификатор в Bitrix", example = "123")
        Long bitrixId,
        
        @Schema(description = "Идентификатор заказа", example = "456")
        Long orderId
) {}