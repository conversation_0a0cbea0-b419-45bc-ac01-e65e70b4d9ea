package ru.oskelly.concierge.controller.dto;


import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Структура описания перечисляемого значения с локализацией")
public record DescriptionStructureEnum(

        @Schema(description = "Код значения (техническое обозначение)", implementation = String.class)
        String code,

        @Schema(description = "Локализованное описание значения", implementation = String.class)
        String localizedDescription
) {
}
