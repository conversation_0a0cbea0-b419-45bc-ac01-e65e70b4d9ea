package ru.oskelly.concierge.controller.dto;

import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

/**
 * DTO для группировки по источнику и количеству
 * @param sourceId - источник
 * @param quantity - количество
 * @param description - описание
 */
public record GroupingSourceQuantity(
        PurchaseOrderSourceEnum sourceId,
        Long quantity,
        String description
) {
    public GroupingSourceQuantity(PurchaseOrderSourceEnum sourceId, Long quantity) {
        this(sourceId, quantity, sourceId.getDescription());
    }
}
