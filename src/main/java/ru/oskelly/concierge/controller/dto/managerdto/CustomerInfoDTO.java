
package ru.oskelly.concierge.controller.dto.managerdto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * DTO с информацией о клиенте
 * <p>
 * Содержит основные данные о клиенте, включая контактную информацию
 * и статус активных блокировок.
 */
@Schema(description = "DTO с основной информацией о клиенте")
public record CustomerInfoDTO(
        @Schema(
                description = "Уникальный идентификатор клиента",
                example = "12345",
                format = "int64"
        )
        Long customerId,

        @Schema(
                description = "Полное имя клиента",
                example = "Иванов Иван Иванович",
                maxLength = 255
        )
        String customerNickName,
        @Schema(
                description = "Имя клиента",
                example = "Иван",
                maxLength = 100
        )
        String lastname,

        @Schema(
                description = "Фамилия клиента",
                example = "Иванов",
                maxLength = 100
        )
        String firstname,

        @Schema(
                description = "Контактный телефон клиента",
                example = "+79161234567",
                pattern = "^\\+[0-9]{1,15}$"
        )
        String customerPhone,

        @Schema(
                description = "Электронная почта клиента",
                example = "<EMAIL>",
                format = "email",
                maxLength = 320
        )
        String customerEmail,
        @Schema(
                description = "Ссылка на картинку аватара",
                example = "https://example.com/avatar.jpg"
        )
        String urlAvatar,

        @Schema(
                description = "Признак наличия активных блокировок у клиента",
                example = "false"
        )
        Boolean hasActiveBans
) {
}