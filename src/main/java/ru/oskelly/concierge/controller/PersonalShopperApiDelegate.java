package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperCreateRequest;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;
import ru.oskelly.concierge.data.model.PersonalShopper;

@Tag(name = "concierge-personal-shopper-controller", description = "API для управления PersonalShopper")
@RequestMapping("/api/v1/personal-shopper")
public interface PersonalShopperApiDelegate {

    @Operation(
            operationId = "createPersonalShopper",
            summary = "Создание нового PersonalShopper",
            description = "Добавляет нового PersonalShopper в систему на основе предоставленных данных",
            responses = @ApiResponse(responseCode = "201", description = "PersonalShopper успешно создан", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = PersonalShopper.class))
            })
    )
    @PostMapping(
            value = "/create",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    default ResponseEntity<PersonalShopper> createPersonalShopper(
            @Parameter(name = "request", description = "Данные для создания PersonalShopper", required = true)
            @RequestBody @Valid PersonalShopperCreateRequest request) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "filterPersonalShopper",
            summary = "Фильтр для списка PersonalShopper",
            description = "Возвращает фильтр для PersonalShopper",
            responses = @ApiResponse(responseCode = "200", description = "Фильтр PersonalShopper успешно получен",
                    content = {@Content(mediaType = "application/json",
                            schema = @Schema(implementation = PersonalShopperFilter.class))
                    })
    )
    @GetMapping(
            value = "/filter",
            produces = {"application/json"}
    )
    default ResponseEntity<PersonalShopperFilter> filterPersonalShopper() {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Получение списка PersonalShopper по фильтру",
            description = "Возвращает список PersonalShopper, отфильтрованных по заданным параметрам.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Успешный запрос. Возвращает список DTO PersonalShopper",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = PaginatedShoppersResult.class)
                            )),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Неверные параметры запроса (невалидный фильтр)"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Внутренняя ошибка сервера"
                    )
            }
    )
    @PostMapping(
            value = "/shoppers",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    default ResponseEntity<PaginatedShoppersResult> getPersonalShoppers(
            @Parameter(
                    name = "filter",
                    description = "Фильтр для получения заявок",
                    required = true,
                    schema = @Schema(implementation = PersonalShopperFilter.class)
            )
            @RequestBody @Valid PersonalShopperFilter filter,
            @Parameter(
                    name = "userId",
                    description = "ID пользователя, выполняющего запрос",
                    required = true,
                    example = "12345"
            ) @RequestParam(name = "userId") Long userId,
            @Parameter(
                    name = "searchText",
                    description = "Текст для полнотекстового поиска по заявкам",
                    example = "срочный заказ"
            ) @RequestParam(name = "searchText", required = false) String searchText
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}