package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.data.model.RejectionReason;
import ru.oskelly.concierge.data.model.enums.ObjectType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.List;

@Validated
@Tag(name = "rejection-reason-controller", description = "API для работы с причинами отклонений")
@RequestMapping("/api/v1/rejection-reasons")
public interface RejectionReasonApiDelegate {
    @Operation(
            operationId = "getRejectionReasons",
            summary = "Получение списка причин отклонения",
            description = "Возвращает список причин отклонения в зависимости от типа объекта и статуса заявки (если применимо).",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Список причин отклонения успешно получен",
                            content = @Content(
                                    mediaType = "application/json",
                                    array = @ArraySchema(schema = @Schema(implementation = RejectionReason.class))
                            )),
                    @ApiResponse(responseCode = "400",
                            description = "Некорректный запрос: статус указан для неверного типа объекта")
            }
    )
    @GetMapping
    default ResponseEntity<List<RejectionReason>> getReasons(
            @Parameter(
                    name = "userId",
                    description = "ID пользователя, выполняющего запрос",
                    required = true,
                    example = "12345"
            ) @RequestParam(name = "userId") Long userId,
            @Parameter(
                    name = "objectType",
                    description = "Тип объекта (заявка или товар)",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam ObjectType objectType,
            @Parameter(
                    name = "status",
                    description = "Статус заявки (только для типа объекта ORDER)",
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) PurchaseOrderStatusEnum status) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
