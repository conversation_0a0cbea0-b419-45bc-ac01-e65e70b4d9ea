package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.service.OrderAssignmentSalesService;
import ru.oskelly.concierge.service.OrderAssignmentSourcerService;
import ru.oskelly.concierge.service.PurchaseOrderService;

@RestController
@RequiredArgsConstructor
public class ManualPurchaseOrderAssignmentController implements ManualPurchaseOrderAssignmentApiDelegate {
    private final PurchaseOrderService purchaseOrderService;
    private final OrderAssignmentSalesService orderAssignmentSalesService;
    private final OrderAssignmentSourcerService orderAssignmentSourcerService;

    @Override
    public ResponseEntity<Void> assignOrderToSales(Long orderId) {
        orderAssignmentSalesService.requestAvailableSales(orderId);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> assignOrderToAdminSales(Long orderId, SalesInfoDTO salesInfo) {
        purchaseOrderService.assignSales(orderId, salesInfo);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> assignOrderToSourcer(Long orderId) {
        orderAssignmentSourcerService.requestAvailableSourcer(orderId);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> assignOrderToAdminSourcer(Long orderId, SourcerInfoDTO sourcerInfo) {
        purchaseOrderService.assignSourcer(orderId, sourcerInfo);
        return ResponseEntity.ok().build();
    }
}
