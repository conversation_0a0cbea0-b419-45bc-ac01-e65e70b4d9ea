package ru.oskelly.concierge.controller.mercaux.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.CurrencyDTO;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.SellerInfoDTO;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Schema(description = "Продукт с платформы для Mercaux")
@JsonInclude(JsonInclude.Include.NON_NULL)
public record MercauxProductPlatformDTO(
        @Schema(description = "Ссылка на оффер", implementation = MercauxOfferReferenceDTO.class)
        MercauxOfferReferenceDTO offer,
        @Schema(description = "Идентификатор товара", example = "1")
        Long productId,
        @Schema(description = "Критерии сравнения")
        @ArraySchema(schema = @Schema(implementation = ComparisonCriterion.class))
        Set<ComparisonCriterion> comparisonCriteria,
        @Schema(description = "Цена в валюте", example = "200.00")
        BigDecimal currencyPrice,
        @Schema(description = "Фото товара", implementation = ImageDTO.class)
        ImageDTO productPhoto,
        @Schema(description = "Расположение товара")
        String productLocation,
        @Schema(description = "Бренд")
        String brand,
        @Schema(description = "Категория товара")
        String productCategory,
        @Schema(description = "Размеры в наличии")
        @ArraySchema(schema = @Schema(implementation = String.class))
        List<String> availableSizes,
        @Schema(description = "Цена без скидки")
        BigDecimal priceWithoutDiscount,
        @Schema(description = "Цена со скидкой")
        BigDecimal priceWithDiscount,
        @Schema(description = "Размер скидки")
        BigDecimal discountAmount,
        @Schema(description = "Тип размера")
        String sizeType,
        @Schema(description = "Идентификатор состояния товара")
        Integer conditionId,
        @Schema(description = "Название состояния товара")
        String conditionName,
        @Schema(description = "Состояние публикации товара")
        String productState,
        @Schema(description = "URL товара")
        String url,
        @Schema(description = "Размер скидки в процентах")
        BigDecimal discount,
        @Schema(description = "Валюта", implementation = CurrencyDTO.class)
        CurrencyDTO currency,
        @Schema(description = "Информация о продавце", implementation = SellerInfoDTO.class)
        SellerInfoDTO sellerInfo
) {
}