package ru.oskelly.concierge.controller.mercaux.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;

import java.util.List;

@Schema(
    description = "DTO с информацией о товаре в заявке для системы Mercaux",
    name = "MercauxShipmentResponseDTO",
    title = "Товар в заявке Mercaux"
)
@JsonInclude(JsonInclude.Include.NON_NULL)
public record MercauxShipmentResponseDTO(
        @Schema(description = "ID товара", example = "1")
        Long id,

        @Schema(description = "ID заявки, к которой относится товар", example = "100")
        Long purchaseOrderId,

        @Schema(description = "ID категории товара", example = "123")
        Long categoryId,

        @Schema(description = "Наименование категории товара", example = "Джемперы и свитеры")
        String categoryName,

        @Schema(description = "ID бренда товара", example = "456")
        Long brandId,

        @Schema(description = "Наименование бренда товара", example = "ЦВЦ СТОУНЗ")
        String brandName,

        @Schema(description = "ID атрибута материала товара", example = "789")
        Long materialAttributeId,

        @Schema(description = "Наименование атрибута материала товара", example = "Синтетика")
        String materialAttributeName,

        @Schema(description = "ID атрибута цвета товара", example = "101")
        Long colorAttributeId,

        @Schema(description = "Наименование атрибута цвета товара", example = "Оранжевый")
        String colorAttributeName,

        @Schema(description = "Дата создания записи о товаре", example = "2023-01-01T12:00:00Z")
        String createdAt,

        @Schema(description = "ID модели товара", example = "202")
        Long modelId,

        @Schema(description = "Наименование модели товара", example = "Twist")
        String modelName,

        @Schema(description = "Размеры товара", implementation = ShimpentSizeDTO.class)
        ShimpentSizeDTO shipmentSize,

        @Schema(description = "Описание товара", example = "Кожаная куртка черного цвета")
        String description,

        @Schema(description = "Список изображений товара")
        @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
        List<ImageDTO> images,

        @Schema(description = "Ссылки на товар", example = "[\"https://example.com/product1\"]")
        @ArraySchema(schema = @Schema(implementation = String.class))
        List<String> links,

        @Schema(description = "Комментарий к товару", example = "Маленькая, бежевая")
        String comment,
        //- отдельный блок - products содержащий набор productPlatformDTO (offer.type = PLATFORM_PRODUCT), если productId = null, блок не добавляется.
        //- отдельный блок - buyerOffers содержащий набор offers(MercauxOfferDTO) (offer.type = BUYER_OFFER) если BUYER_OFFER, то все ProposedOffer идут в этот блок. Блоки добавляются всегда.
        @Schema(description = "Список продуктов с платформы")
        @ArraySchema(schema = @Schema(implementation = MercauxProductPlatformDTO.class))
        List<MercauxProductPlatformDTO> products,
        @Schema(description = "Предложения покупателя для Mercaux")
        @ArraySchema(schema = @Schema(implementation = MercauxBuyerOffers.class))
        List<MercauxBuyerOffers> buyerOffers
) {

    public MercauxShipmentResponseDTO(Long id,
                               Long purchaseOrderId,
                               Long categoryId,
                               String categoryName,
                               Long brandId,
                               String brandName,
                               Long materialAttributeId,
                               String materialAttributeName,
                               Long colorAttributeId,
                               String colorAttributeName,
                               String createdAt,
                               Long modelId,
                               String modelName,
                               ShimpentSizeDTO shipmentSize,
                               String description,
                               List<ImageDTO> images,
                               List<String> links,
                               String comment) {
        this(
                id,
                purchaseOrderId,
                categoryId,
                categoryName,
                brandId,
                brandName,
                materialAttributeId,
                materialAttributeName,
                colorAttributeId,
                colorAttributeName,
                createdAt,
                modelId,
                modelName,
                shipmentSize,
                description,
                images,
                links,
                comment,
                null,
                null);
    }
}
