package ru.oskelly.concierge.controller.mercaux.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Предложения покупателей для Mercaux")
public record MercauxBuyerOffers(
        @Schema(description = "Идентификатор offer")
        Long id,
        @Schema(description = "Информация о шопере", implementation = ShopperInfoDTO.class)
        ShopperInfoDTO shopperInfo,
        @Schema(description = "Предложенные офферы")
        @ArraySchema(schema = @Schema(implementation = MercauxEnhancedProposedOfferDTO.class))
        List<MercauxEnhancedProposedOfferDTO> proposedOffers
) {
}