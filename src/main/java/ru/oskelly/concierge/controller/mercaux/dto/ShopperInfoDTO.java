package ru.oskelly.concierge.controller.mercaux.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;

import java.util.Set;

@Schema(description = "Информация о шопере")
public record ShopperInfoDTO(
        @Schema(description = "Идентификатор шопера")
        Long id,
        @Schema(description = "Идентификатор пользователя (шопера)")
        Long userId,
        @Schema(description = "Никнейм шопера")
        String nickName,
        @Schema(description = "ФИО шопера")
        String fio,
        @Schema(description = "URL аватара")
        String urlAvatar,
        @Schema(description = "Формат платежа", implementation = DescriptionStructureEnum.class)
        DescriptionStructureEnum paymentFormat,
        @Schema(description = "Типы взаимодействия")
        @ArraySchema(schema = @Schema(implementation = DescriptionStructureEnum.class))
        Set<DescriptionStructureEnum> interactionTypes
) {
}