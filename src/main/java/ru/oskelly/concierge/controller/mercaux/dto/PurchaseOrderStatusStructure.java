package ru.oskelly.concierge.controller.mercaux.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Структура статуса заказа на покупку")
public record PurchaseOrderStatusStructure(

        @Schema(description = "Код статуса заказа", example = "PENDING")
        String code,

        @Schema(description = "Локализованное название статуса", example = "В ожидании")
        String localize
) {
}
