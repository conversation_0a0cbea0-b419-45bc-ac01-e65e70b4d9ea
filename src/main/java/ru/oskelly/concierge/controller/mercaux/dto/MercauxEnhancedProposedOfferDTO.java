package ru.oskelly.concierge.controller.mercaux.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.CurrencyDTO;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Set;

@Schema(description = "Расширенное предложение для Mercaux")
public record MercauxEnhancedProposedOfferDTO(
        @Schema(description = "Идентификатор предложения")
        Long id,
        @Schema(description = "Критерии сравнения")
        @ArraySchema(schema = @Schema(implementation = ComparisonCriterion.class))
        Set<ComparisonCriterion> comparisonCriteria,
        @Schema(description = "Цена в рублях")
        BigDecimal rublePrice,
        @Schema(description = "Дата доставки")
        ZonedDateTime deliveryDate,
        @Schema(description = "Срок действия")
        ZonedDateTime validUntil,
        @Schema(description = "Дата создания")
        ZonedDateTime creationDate,
        @Schema(description = "Валюта", implementation = CurrencyDTO.class)
        CurrencyDTO currency
) {
}