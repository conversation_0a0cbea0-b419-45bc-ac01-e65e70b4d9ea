package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.OrderSources;
import ru.oskelly.concierge.controller.dto.PaginatedResult;
import ru.oskelly.concierge.controller.dto.PurchaseOrderCreateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFilter;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderUpdateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderWithShipmentsCreateRequest;
import ru.oskelly.concierge.controller.dto.RejectionEventRequest;
import ru.oskelly.concierge.controller.dto.RequestsFilter;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.service.PurchaseOrderService;
import ru.oskelly.concierge.service.RejectionReasonService;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class PurchaseController implements PurchaseOrderApiDelegate {
    private final PurchaseOrderService purchaseOrderService;
    private final RejectionReasonService rejectionReasonService;

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> createPurchaseOrder(PurchaseOrderCreateRequest createRequest, Long userId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok().body(purchaseOrderService.createPurchaseOrder(createRequest));
    }

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> createPurchaseOrderWithShipments(
        PurchaseOrderWithShipmentsCreateRequest createRequest,
        Long userId
    ) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(purchaseOrderService.createPurchaseOrderWithShipments(createRequest));
    }

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> eventSend(Long orderId,
                                                          PurchaseOrderTransitionEvent eventCode,
                                                          Long userId, Roles role) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);

        // Если роль не определена, то считаем что это вызов из мобильного приложения
        return ResponseEntity.ok().body(
                purchaseOrderService.eventProcessing(orderId, eventCode, role == null ? Roles.CUSTOMER : role)
        );
    }

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> getPurchaseOrder(Long orderId, Roles role, Long customerId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, customerId);
        return ResponseEntity.ok().body(purchaseOrderService.getPurchaseOrder(orderId, role, customerId));
    }

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> createRejectionEvent(RejectionEventRequest request, Long userId, Long orderId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok()
                .body(rejectionReasonService.createRejectionEvent(request, orderId));
    }

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> updatePurchaseOrder(Long orderId,
                                                                    Long userId,
                                                                    PurchaseOrderUpdateRequest updateRequest) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok().body(purchaseOrderService.updatePurchaseOrder(orderId, userId, updateRequest));
    }

    @Override
    public ResponseEntity<PurchaseOrderFilter> getFilterPurchaseOrders(PurchaseOrderFilter filter, Long userId, Roles role) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok().body(purchaseOrderService.getPurchaseOrderStatusCountsAndStatuses(filter, role));
    }

    @Override
    public ResponseEntity<PaginatedResult> getPurchaseOrders(RequestsFilter filter, Long userId, Roles role, String searchText) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok().body(purchaseOrderService.getPurchaseOrders(filter, searchText, role));
    }

    @Override
    public ResponseEntity<OrderSources> getSources() {
        return ResponseEntity.ok(purchaseOrderService.getSources());
    }

    @Override
    public ResponseEntity<PurchaseOrderFullDTO> returnToWork(Long userId, Long orderId, String comment) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(rejectionReasonService.returnToWork(userId, orderId, comment));
    }

    @Override
    public ResponseEntity<List<OfferPairDTO>> processOrder(OrderInfoDTO orderInfo) {
        List<OfferPairDTO> offerPairs = purchaseOrderService.processOrderInfo(orderInfo);
        return ResponseEntity.ok(offerPairs);
    }
}
