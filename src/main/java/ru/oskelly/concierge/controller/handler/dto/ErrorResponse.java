package ru.oskelly.concierge.controller.handler.dto;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Builder
public class ErrorResponse {
    private final Long userId;
    private final String message;
    private final Integer httpCode;
    private final Long orderId;
    private final String errorId;
    private final LocalDateTime timestamp;
    private final String path;
    private final Map<String, ?> additionalDetails;
}
