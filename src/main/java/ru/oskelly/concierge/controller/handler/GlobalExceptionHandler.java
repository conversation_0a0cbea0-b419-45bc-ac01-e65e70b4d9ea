package ru.oskelly.concierge.controller.handler;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.handler.dto.ErrorResponse;
import ru.oskelly.concierge.exception.ConciergeAbstractException;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Обработчик исключений
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {
    private static final String APP_PACKAGE_PREFIX = "ru.oskelly";

    @ExceptionHandler(ConciergeAbstractException.class)
    public ResponseEntity<ErrorResponse> handleStateMachineInvalidTransitionException(
            ConciergeAbstractException ex,
            WebRequest request,
            HttpServletRequest httpRequest) {

        String errorId = UUID.randomUUID().toString();
        log.error("Error occurred. ErrorId: {}", errorId, ex);

        String appStackTrace = getApplicationStackTrace(ex);
        applicationStackTraceLog(errorId, appStackTrace);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .userId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class))
                .message(String.format("Ошибка -> %s", ex.getMessage()))
                .httpCode(ex.getHttpCode())
                .orderId(ex.getOrderId())
                .errorId(errorId)
                .timestamp(LocalDateTime.now())
                .path(httpRequest.getRequestURI())
                .build();
        ThreadLocalContext.clean();
        return ResponseEntity
                .status(ex.getHttpCode() != null ? ex.getHttpCode() : HttpStatus.INTERNAL_SERVER_ERROR.value())
                .body(errorResponse);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
        @Nullable MethodArgumentNotValidException ex,
        @Nullable HttpHeaders headers,
        @Nullable HttpStatusCode status,
        @Nullable WebRequest request
    ) {
        Objects.requireNonNull(ex);
        Objects.requireNonNull(request);

        String errorId = UUID.randomUUID().toString();
        log.error("Произошла ошибка валидации. ErrorId: {}", errorId, ex);

        String appStackTrace = getApplicationStackTrace(ex);
        applicationStackTraceLog(errorId, appStackTrace);

        Map<String, List<String>> errors = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .collect(Collectors.groupingBy(
                FieldError::getField,
                Collectors.mapping(
                    fieldError -> Objects.toString(fieldError.getDefaultMessage(), ""),
                    Collectors.toList()
                )
            ));

        ErrorResponse errorResponse = ErrorResponse.builder()
            .userId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class))
            .message(String.format("Ошибка -> %s", "Возникли ошибки валидации входных параметров"))
            .httpCode(HttpStatus.BAD_REQUEST.value())
            .orderId(null)
            .errorId(errorId)
            .timestamp(LocalDateTime.now())
            .path(request.getContextPath())
            .additionalDetails(errors)
            .build();

        ThreadLocalContext.clean();

        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST.value())
            .body(errorResponse);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleConstraintViolationException(
        ConstraintViolationException ex,
        WebRequest request,
        HttpServletRequest httpRequest
    ) {
        String errorId = UUID.randomUUID().toString();
        log.error("Произошла ошибка валидации. ErrorId: {}", errorId, ex);

        String appStackTrace = getApplicationStackTrace(ex);
        applicationStackTraceLog(errorId, appStackTrace);

        Map<String, List<String>> errors = ex.getConstraintViolations()
            .stream()
            .collect(Collectors.groupingBy(
                constraintViolation ->  constraintViolation.getPropertyPath().toString(),
                Collectors.mapping(
                    ConstraintViolation::getMessage,
                    Collectors.toList()
                )
            ));

        ErrorResponse errorResponse = ErrorResponse.builder()
            .userId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class))
            .message(String.format("Ошибка -> %s", "Возникли ошибки валидации входных параметров"))
            .httpCode(HttpStatus.BAD_REQUEST.value())
            .orderId(null)
            .errorId(errorId)
            .timestamp(LocalDateTime.now())
            .path(httpRequest.getRequestURI())
            .additionalDetails(errors)
            .build();

        ThreadLocalContext.clean();

        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST.value())
            .body(errorResponse);
    }

    // Обработка других исключений
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex,
            WebRequest request,
            HttpServletRequest httpRequest) {
        String errorId = UUID.randomUUID().toString();

        // Логирование полного исключения
        log.error("Unexpected error occurred. ErrorId: {}", errorId, ex);

        // Логирование только стек-трейса приложения
        String appStackTrace = getApplicationStackTrace(ex);
        applicationStackTraceLog(errorId, appStackTrace);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .userId(ThreadLocalContext.get(ContextConstants.USER_ID, Long.class))
                .message(String.format("Внутренняя ошибка сервера -> %s", appStackTrace))
                .httpCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .errorId(errorId)
                .timestamp(LocalDateTime.now())
                .path(httpRequest.getRequestURI())
                .build();

        ThreadLocalContext.clean();
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(errorResponse);
    }

    /**
     * Извлекает и форматирует стек-трейс, относящийся только к классам приложения
     * @param ex Исключение для анализа
     * @return Строка, содержащая отфильтрованный стек-трейс
     */
    private String getApplicationStackTrace(Throwable ex) {
        if (ex == null) {
            return "";
        }

        StackTraceElement[] stackTrace = ex.getStackTrace();
        StringBuilder appStackTrace = new StringBuilder();

        for (StackTraceElement element : stackTrace) {
            if (element.getClassName().startsWith(APP_PACKAGE_PREFIX)) {
                appStackTrace.append("\n    at ").append(element);
            }
        }

        return appStackTrace.toString();
    }

    private void applicationStackTraceLog(String errorId, String appStackTrace) {
        log.error("Стек вызовов ошибки с идентификатором {}: {}", errorId, appStackTrace);
    }
}
