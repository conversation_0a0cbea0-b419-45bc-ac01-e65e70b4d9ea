package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.controller.dto.CommentChangeRequest;
import ru.oskelly.concierge.controller.dto.CommentFullDTO;
import ru.oskelly.concierge.controller.dto.CommentListDTO;
import ru.oskelly.concierge.service.CommentService;

@RestController
@RequiredArgsConstructor
public class CommentController implements CommentApiDelegate {
    private final CommentService commentService;

    @Override
    public ResponseEntity<CommentListDTO> getComments(Long orderId) {

        return ResponseEntity.ok().body(commentService.getComments(orderId));
    }

    @Override
    public ResponseEntity<CommentFullDTO> addNewComment(Long orderId, CommentChangeRequest createRequest) {
        return ResponseEntity.ok().body(commentService.addNewComment(orderId, createRequest));
    }

    @Override
    public ResponseEntity<CommentFullDTO> getComment(Long orderId, Long id) {
        return ResponseEntity.ok().body(commentService.getComment(orderId, id));
    }

    @Override
    public void deleteComment(Long orderId, Long id) {
        commentService.deleteComment(orderId, id);
    }

    @Override
    public ResponseEntity<CommentFullDTO> pinComment(Long orderId, Long id) {
        return ResponseEntity.ok().body(commentService.pinComment(orderId, id));
    }

    @Override
    public ResponseEntity<CommentFullDTO> editComment(Long orderId, Long id, CommentChangeRequest changeRequest) {
        return ResponseEntity.ok().body(commentService.editComment(orderId, id, changeRequest));
    }
}
