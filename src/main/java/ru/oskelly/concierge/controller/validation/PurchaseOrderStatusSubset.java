package ru.oskelly.concierge.controller.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Валидация поля статуса заявки по списку некоторых его значений
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PurchaseOrderStatusSubsetValidator.class)
public @interface PurchaseOrderStatusSubset {
    String message() default "Недопустимое значение";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};

    PurchaseOrderStatusEnum[] anyOf();
}
