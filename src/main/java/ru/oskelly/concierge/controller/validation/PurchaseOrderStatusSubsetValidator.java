package ru.oskelly.concierge.controller.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Валидатор для проверки поля статуса заявки на предмет вхождения в указанный список значений
 */
public class PurchaseOrderStatusSubsetValidator implements ConstraintValidator<PurchaseOrderStatusSubset, PurchaseOrderStatusEnum> {

    private Set<PurchaseOrderStatusEnum> allowedValues;
    private String allowedValuesAsString;

    @Override
    public void initialize(PurchaseOrderStatusSubset constraintAnnotation) {
        allowedValues = new HashSet<>(Arrays.asList(constraintAnnotation.anyOf()));
        allowedValuesAsString = allowedValues.stream()
            .map(Enum::name)
            .sorted()
            .collect(Collectors.joining(", "));
    }

    @Override
    public boolean isValid(PurchaseOrderStatusEnum status, ConstraintValidatorContext context) {
        if (allowedValues.contains(status)) {
            return true;
        }

        context.disableDefaultConstraintViolation();

        String error = "Недопустимый начальный статус заявки. Разрешены только: " + allowedValuesAsString;

        context
            .buildConstraintViolationWithTemplate(error)
            .addConstraintViolation();

        return false;
    }
}
