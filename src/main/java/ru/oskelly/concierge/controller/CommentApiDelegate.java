package ru.oskelly.concierge.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import ru.oskelly.concierge.controller.dto.CommentChangeRequest;
import ru.oskelly.concierge.controller.dto.CommentFullDTO;
import ru.oskelly.concierge.controller.dto.CommentListDTO;

@Validated
@Tag(name = "concierge-comments-controller", description = "API для взаимодействия с комментариями к заявкам на покупку")
@RequestMapping("/api/v1/purchase/{orderId}/comments")
public interface CommentApiDelegate {
    @Operation(
            operationId = "getPurchaseOrderComments",
            summary = "Список комментариев по заявке",
            description = "Список комментариев по заявке",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Список комментариев", content = {
                        @Content(mediaType = "application/json", schema = @Schema(implementation = CommentListDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Не найдена заявка по запросу"),
                    @ApiResponse(responseCode = "500", description = "Внутренняя ошибка")
            }
    )
    @GetMapping(
            produces = {"application/json"}
    )
    default ResponseEntity<CommentListDTO> getComments(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "addNewComment",
            summary = "Добавление нового комментария",
            description = "Добавление нового комментария к заявке",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Комментарий успешно создан", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = CommentFullDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Не найдена заявка по запросу"),
                    @ApiResponse(responseCode = "500", description = "Внутренняя ошибка")
            }
    )
    @PostMapping(
            produces = {"application/json"}
    )
    default ResponseEntity<CommentFullDTO> addNewComment(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId,
            @RequestBody(description = "Данные нового комментария", required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = CommentChangeRequest.class)) )
            @Validated @org.springframework.web.bind.annotation.RequestBody CommentChangeRequest createRequest) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "getComment",
            summary = "Получение комментария",
            description = "Получение комментария к заявке",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Комментарий получен", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = CommentFullDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Не найден комментарий"),
                    @ApiResponse(responseCode = "500", description = "Внутренняя ошибка")
            }
    )
    @GetMapping(
            value = "/{id}",
            produces = {"application/json"}
    )
    default ResponseEntity<CommentFullDTO> getComment(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId,
            @Parameter(name = "id", description = "ID Комментария", required = true) @PathVariable Long id) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "deleteComment",
            summary = "Удаление комментария",
            description = "Удаление комментария к заявке",
            responses = @ApiResponse(responseCode = "200", description = "Комментарий удален")
    )
    @DeleteMapping(
            value = "/{id}"
    )
    default void deleteComment(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId,
            @Parameter(name = "id", description = "ID Комментария", required = true) @PathVariable Long id) {
    }

    @Operation(
            operationId = "pinComment",
            summary = "Пин выбранного комментария",
            description = "Пин комментария к заявке",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Комментарий запинен", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = CommentFullDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Не найдена заявка или комментарий по запросу"),
                    @ApiResponse(responseCode = "500", description = "Внутренняя ошибка")
            }
    )
    @PatchMapping(
            value = "/{id}/pin",
            produces = {"application/json"}
    )
    default ResponseEntity<CommentFullDTO> pinComment(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId,
            @Parameter(name = "id", description = "ID Комментария", required = true) @PathVariable Long id) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            operationId = "editComment",
            summary = "Изменение комментария",
            description = "Изменение комментария к заявке",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Комментарий успешно изменен", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = CommentFullDTO.class))}),
                    @ApiResponse(responseCode = "404", description = "Не найдена заявка или комментарий по запросу"),
                    @ApiResponse(responseCode = "500", description = "Внутренняя ошибка")
            }
    )
    @PatchMapping(
            value = "/{id}",
            produces = {"application/json"}
    )
    default ResponseEntity<CommentFullDTO> editComment(
            @Parameter(name = "orderId", description = "ID заявки", required = true) @PathVariable Long orderId,
            @Parameter(name = "id", description = "ID Комментария", required = true) @PathVariable Long id,
            @RequestBody(description = "Данные измененного комментария", required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = CommentChangeRequest.class)) )
            @Validated @org.springframework.web.bind.annotation.RequestBody CommentChangeRequest changeRequest) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
