package ru.oskelly.concierge.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.SalesManagerProductDto;
import ru.oskelly.concierge.service.SalesManagerService;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class SalesManagerController implements SalesManagerApiDelegate {
    private final SalesManagerService salesManagerService;
    @Override
    public ResponseEntity<List<SalesManagerProductDto>> getSalesOrders(Long userId, Long shipmentId) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        return ResponseEntity.ok(salesManagerService.getSalesOrders(shipmentId));
    }
}
