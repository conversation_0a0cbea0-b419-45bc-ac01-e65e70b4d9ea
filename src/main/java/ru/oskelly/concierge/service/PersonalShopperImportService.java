package ru.oskelly.concierge.service;

import ru.oskelly.concierge.bitrix.dto.ShopperResponse;

import java.util.List;

/**
 * Сервис для импорта шопперов.
 */
public interface PersonalShopperImportService {

    /**
     * Импортирует список шопперов.
     * @param shoppers список {@link ShopperResponse}, содержащий данные по каждым шопперу.
     */
    void importShoppers(List<ShopperResponse> shoppers);

}
