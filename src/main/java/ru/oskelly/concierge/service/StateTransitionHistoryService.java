package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.List;

public interface StateTransitionHistoryService {
    /**
     * Получение истории переходов
     * @param purchaseOrderId - идентификатор заявки
     * @return - история переходов
     */
    List<PurchaseOrderStateHistoryDTO> getTransitionHistory(Long purchaseOrderId);

    /**
     * Проверка, переходила ли заявка в переданный статус
     * @param purchaseOrderId - идентификатор заявки
     * @param status - проверяемый статус заявки
     * */
    boolean checkStatusPassed(Long purchaseOrderId, PurchaseOrderStatusEnum status);
}
