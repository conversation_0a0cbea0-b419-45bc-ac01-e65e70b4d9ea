package ru.oskelly.concierge.service.component;

import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;

public interface PersonalShopperComponent {
    /**
     * Фильтр для шоперов
     *
     * @return - фильтр шоперов
     */
    PersonalShopperFilter filterPersonalShopper();
    /**
     * Получение списка шоперов
     *
     * @param filter     - фильтр шоперов
     * @param searchText - текст поиска
     * @return - список шоперов
     */
    PaginatedShoppersResult getPersonalShoppers(PersonalShopperFilter filter, String searchText);
}
