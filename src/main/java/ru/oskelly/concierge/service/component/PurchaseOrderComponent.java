package ru.oskelly.concierge.service.component;

import ru.oskelly.concierge.controller.dto.PaginatedResult;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFilter;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStats;
import ru.oskelly.concierge.controller.dto.RequestsFilter;
import ru.oskelly.concierge.data.model.enums.Roles;

public interface PurchaseOrderComponent {
    /**
     * Получаем статистику заявок
     * @param filter - фильтр
     * @param userId - идентификатор пользователя
     * @param role - роль пользователя
     * @return - статистика заявок
     */
    PurchaseOrderStats getPurchaseOrderStats(PurchaseOrderFilter filter, Long userId, Roles role);

    /**
     * Получаем список заявок, соответствующих фильтру
     * findAllWithFilter(filter, searchText, "date") - для сортировки по дате (по умолчанию)
     * findAllWithFilter(filter, searchText, "status") - для сортировки по статусу
     *
     * @param filter     - фильтр
     * @param searchText - текст для поиска
     * @return - список заявок
     */
    PaginatedResult findAllWithFilter(RequestsFilter filter, Long userId, String searchText, Roles role);

}
