package ru.oskelly.concierge.service.component;

import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;

import java.util.List;

/**
 * Компонент, обрабатывающий заявки на покупку из монолита
 */
public interface WorkerOrdes {

    /**
     * Обрабатывает информацию о заказе, обновляя связанные заказы на покупку.
     * Проверяет входные данные, получает заказы из базы данных, обновляет их
     * и переводит статус в PAYED_BY_CUSTOMER, если необходимо.
     * Возвращает пары [bitrixId, orderId] для предложений с isSentToCustomer = true.
     *
     * @param orderInfo информация о заказе, содержащая идентификаторы продуктов,
     *                  клиента и заказа
     * @return список пар [bitrixId, orderId] для предложений, отправленных клиенту
     * @throws ru.oskelly.concierge.exception.ValidationException если входные данные некорректны
     */
    List<OfferPairDTO> processOrderInfo(OrderInfoDTO orderInfo);
}
