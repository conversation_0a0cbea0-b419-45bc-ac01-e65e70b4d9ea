package ru.oskelly.concierge.service.component;

import com.querydsl.core.types.dsl.BooleanExpression;
import ru.oskelly.concierge.data.model.QPurchaseOrder;
import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * Компонент построения фильтра по ролям для поиска заявок
 */
public interface PurchaseOrderRoleFilterBuilder {

    /**
     * Построение фильтра по роли пользователя для поиска заявок на покупку
     *
     * @param role роль текущего пользователя
     * @param purchaseOrder сущность заявки в формате QueryDSL
     * @param userId идентификатор текущего пользователя
     * @return QueryDSL-выражение
     */
    BooleanExpression build(Roles role, QPurchaseOrder purchaseOrder, Long userId);
}
