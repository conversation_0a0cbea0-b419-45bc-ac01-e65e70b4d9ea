package ru.oskelly.concierge.service.component;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.data.model.QPurchaseOrder;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.exception.UnsupportedRoleException;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiFunction;

import static ru.oskelly.concierge.data.model.enums.Roles.CONCIERGE_SALES_ADMIN;
import static ru.oskelly.concierge.data.model.enums.Roles.CONCIERGE_SOURCERS_ADMIN;
import static ru.oskelly.concierge.data.model.enums.Roles.KUZNETSKY_BRIDGE_ADMIN;
import static ru.oskelly.concierge.data.model.enums.Roles.KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN;
import static ru.oskelly.concierge.data.model.enums.Roles.MERCAUX_ADMIN;
import static ru.oskelly.concierge.data.model.enums.Roles.SALES;
import static ru.oskelly.concierge.data.model.enums.Roles.SOURCER;
import static ru.oskelly.concierge.data.model.enums.Roles.STOLESHNIKOV_ADMIN;
import static ru.oskelly.concierge.data.model.enums.Roles.STOLESHNIKOV_BOUTIQUE_SALESMAN;

/**
 * Компонент создания фильтров по ролям пользователей для поиска заявок
 */
@Component
public class DefaultPurchaseOrderRoleFilterBuilder implements PurchaseOrderRoleFilterBuilder {

    private final Map<Roles, BiFunction<QPurchaseOrder, Long, BooleanExpression>> FILTERS = new EnumMap<>(Roles.class);

    {
        FILTERS.put(MERCAUX_ADMIN, this::mercauxAdminExpression);
        FILTERS.put(KUZNETSKY_BRIDGE_ADMIN, this::kuznetskyBridgeAdminExpression);
        FILTERS.put(STOLESHNIKOV_ADMIN, this::stoleshnikovAdminExpression);
        FILTERS.put(CONCIERGE_SALES_ADMIN, this::conciergeSalesAdminExpression);
        FILTERS.put(CONCIERGE_SOURCERS_ADMIN, this::conciergeSourcersAdminExpression);
        FILTERS.put(KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN, this::kuznetskyBridgeBoutiqueSalesmanExpression);
        FILTERS.put(STOLESHNIKOV_BOUTIQUE_SALESMAN, this::stoleshnikovBoutiqueSalesmanExpression);
        FILTERS.put(SOURCER, this::sourcerExpression);
        FILTERS.put(SALES, this::salesExpression);
    }

    public BooleanExpression build(Roles role, QPurchaseOrder purchaseOrder, Long userId) {
        return Optional.ofNullable(FILTERS.get(role))
            .map(filter -> filter.apply(purchaseOrder, userId))
            .orElseThrow(() -> new UnsupportedRoleException(
                String.format("Роль %s не поддерживается для поиска заявок", role), null, null, 400)
            );
    }

    /**
     * Супер-админ Mercaux видит все заявки
     */
    private BooleanExpression mercauxAdminExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return Expressions.TRUE;
    }

    /**
     * Админ Кузнецкий мост видит свои заявки и заявки своих продавцов
     */
    private BooleanExpression kuznetskyBridgeAdminExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.salesRole
            .eq(KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN)
            .or(purchaseOrder.salesId.eq(userId));
    }

    /**
     * Админ Столешников видит свои заявки и заявки своих продавцов
     */
    private BooleanExpression stoleshnikovAdminExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.salesRole
            .eq(STOLESHNIKOV_BOUTIQUE_SALESMAN)
            .or(purchaseOrder.salesId.eq(userId));
    }

    /**
     * Админ Консьерж Сейлз видит свои заявки и заявки сейлзов
     */
    private BooleanExpression conciergeSalesAdminExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.salesRole
            .eq(SALES)
            .or(purchaseOrder.salesId.eq(userId));
    }

    /**
     * Админ Консьерж Сорсеры видит заявки всех сорсеров
     */
    private BooleanExpression conciergeSourcersAdminExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.sourcerId.isNotNull();
    }

    /**
     * Продавец бутика Кузнецкий мост видит свои заявки
     */
    private BooleanExpression kuznetskyBridgeBoutiqueSalesmanExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.salesId.eq(userId);
    }

    /**
     * Продавец бутика Столешников видит свои заявки
     */
    private BooleanExpression stoleshnikovBoutiqueSalesmanExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.salesId.eq(userId);
    }

    /**
     * Сорсер видит свои заявки
     */
    private BooleanExpression sourcerExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.sourcerId.eq(userId);
    }

    /**
     * Сейлз видит свои заявки и заявки клиентов, назначенные на сейлза
     */
    private BooleanExpression salesExpression(QPurchaseOrder purchaseOrder, Long userId) {
        return purchaseOrder.salesId.eq(userId);
    }
}
