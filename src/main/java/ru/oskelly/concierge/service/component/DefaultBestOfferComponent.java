package ru.oskelly.concierge.service.component;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.repository.ShipmentRepository;
import ru.oskelly.concierge.exception.ShipmentNotFoundException;
import ru.oskelly.concierge.service.dto.BestOfferDTO;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

@Component
@RequiredArgsConstructor
public class DefaultBestOfferComponent implements BestOfferComponent {

    private final ShipmentRepository shipmentRepository;

    @Override
    public List<BestOfferDTO> findBestOffers(Shipment shipment) {
        if (shipment == null || shipment.getOffers() == null) {
            return new ArrayList<>();
        }

        Map<Long, BestOfferDTO> bestOffersMap = new HashMap<>();

        List<ProposedOffer> proposedOffers = shipment.getOffers().stream()
                .filter(Objects::nonNull)
                .flatMap(offer -> offer.getProposedOffers() != null ?
                        offer.getProposedOffers().stream() : Stream.empty())
                .toList();

        addBestByDeliveryDate(proposedOffers, bestOffersMap);
        addBestByPrice(proposedOffers, bestOffersMap);

        return new ArrayList<>(bestOffersMap.values());
    }

    private void addBestByDeliveryDate(List<ProposedOffer> proposedOffers, Map<Long, BestOfferDTO> map) {
        proposedOffers.stream()
                .map(ProposedOffer::getDeliveryDate)
                .filter(Objects::nonNull)
                .min(Comparator.naturalOrder()).ifPresent(earliestDate -> proposedOffers.stream()
                        .filter(po -> po.getDeliveryDate() != null &&
                                po.getDeliveryDate().equals(earliestDate))
                        .forEach(best -> addOrUpdateCriteria(map, best,
                                ComparisonCriterion.DELIVERY_DATE)));
    }

    private void addBestByPrice(List<ProposedOffer> proposedOffers,
                                Map<Long, BestOfferDTO> map) {

        proposedOffers.stream()
                .map(ProposedOffer::getRublePrice)
                .filter(Objects::nonNull)
                .min(BigDecimal::compareTo).ifPresent(bestProposedPrice -> proposedOffers.stream()
                        .filter(po -> po.getRublePrice() != null &&
                                po.getRublePrice().compareTo(bestProposedPrice) == 0)
                        .forEach(best -> addOrUpdateCriteria(map, best,
                                ComparisonCriterion.PRICE)));

    }


    private void addOrUpdateCriteria(Map<Long, BestOfferDTO> map,
                                     ProposedOffer proposedOffer,
                                     ComparisonCriterion criterion) {
        Long key = proposedOffer.getId();
        BestOfferDTO dto = map.computeIfAbsent(key, k -> {
            BestOfferDTO newDto = new BestOfferDTO();
            newDto.setProposedOffer(proposedOffer);
            newDto.setOffer(proposedOffer.getOffer());
            newDto.setCriteria(new HashSet<>());
            return newDto;
        });
        dto.getCriteria().add(criterion);
    }


    @Override
    public List<BestOfferDTO> findBestOffers(Long shipmentId) {
        if (shipmentId == null) {
            return new ArrayList<>();
        }
        Shipment shipment = shipmentRepository.findById(shipmentId)
                .orElseThrow(() -> new ShipmentNotFoundException(
                        "Товар с ID " + shipmentId + " не найден", null, shipmentId, 404));
        return findBestOffers(shipment);
    }
}