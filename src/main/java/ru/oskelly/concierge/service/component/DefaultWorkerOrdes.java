package ru.oskelly.concierge.service.component;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.exception.ValidationException;
import ru.oskelly.concierge.service.PurchaseOrderService;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.ArrayList;
import java.util.stream.StreamSupport;

@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultWorkerOrdes implements WorkerOrdes {

    private final PurchaseOrderRepository purchaseOrderRepository;
    private final PurchaseOrderService purchaseOrderService;

    /**
     * Обрабатывает информацию о заказе, обновляя связанные заказы на покупку.
     * Проверяет входные данные, получает заказы из базы данных, обновляет их
     * и переводит статус в PAYED_BY_CUSTOMER, если необходимо.
     * Возвращает пары [bitrixId, orderId] для предложений с isSentToCustomer = true.
     *
     * @param orderInfo информация о заказе, содержащая идентификаторы продуктов,
     *                  клиента и заказа
     * @return список пар [bitrixId, orderId] для предложений, отправленных клиенту
     * @throws ValidationException если входные данные некорректны
     */
    @Override
    @Transactional
    public List<OfferPairDTO> processOrderInfo(OrderInfoDTO orderInfo) {
        validateOrderInfo(orderInfo);
        log.info("Обработка информации о заказе: orderID={}, clientId={}, productIds={}",
                orderInfo.orderID(), orderInfo.clientId(), orderInfo.productIds());

        List<OfferPairDTO> offerPairs = new ArrayList<>();
        List<PurchaseOrder> purchaseOrders = purchaseOrderRepository
                .findByProductIdIn(orderInfo.productIds());

        if (purchaseOrders.isEmpty()) {
            log.info("Заказы на покупку для productIds={} не найдены", orderInfo.productIds());
            return offerPairs;
        }

        Map<Long, List<PurchaseOrder>> ordersByProductId = groupOrdersByProductId(purchaseOrders, orderInfo.productIds());
        Set<PurchaseOrder> ordersToSave = new HashSet<>();
        int totalProcessed = 0;

        for (Long productId : orderInfo.productIds()) {
            List<PurchaseOrder> matchingOrders = ordersByProductId.getOrDefault(productId, List.of());
            if (matchingOrders.isEmpty()) {
                log.debug("Нет заказов для productId={}", productId);
                continue;
            }
            for (PurchaseOrder order : matchingOrders) {
                try {
                    boolean isCustomerMatch = Objects.equals(order.getCustomerId(), orderInfo.clientId());
                    boolean changed = processOrder(order, productId, orderInfo, isCustomerMatch, offerPairs);
                    if (changed) {
                        ordersToSave.add(order);
                    }
                    if (isCustomerMatch) {
                        processOrderStatusTransition(order);
                    }
                    totalProcessed++;
                } catch (Exception e) {
                    log.error("Ошибка обработки заказа orderId={} для productId={}: {}",
                            order.getId(), productId, e.getMessage(), e);
                }
            }
        }

        if (!ordersToSave.isEmpty()) {
            purchaseOrderRepository.saveAll(ordersToSave);
            log.info("Сохранено {} обновленных заказов на покупку", ordersToSave.size());
        }
        log.info("Обработано {} заказов, найдено {} пар [bitrixId, orderId] для предложений с isSentToCustomer = true", totalProcessed, offerPairs.size());
        return offerPairs;
    }

    /**
     *   Метод группирует заказы на покупку по идентификаторам продуктов, анализируя структуру: заказы → отгрузки → предложения → продукты.
     *   Для каждого заказа проверяются отгрузки и предложения, включая proposedOffers, на наличие указанных productIds.
     *   Связывает заказы с productId, игнорируя предложения без искомых продуктов.
     *   Поддерживает null-значения и случаи, когда offer.getProductId() == null.
     * 
     * @param purchaseOrders список заказов на покупку для анализа. Не должен быть null.
     *                      Каждый заказ должен содержать коллекцию (shipments),
     *                      которые в свою очередь содержат предложения (offers)
     * @param productIds набор идентификаторов продуктов, по которым необходимо сгруппировать заказы.
     *                  Не должен быть null. Пустой набор приведет к возврату пустой карты
     * 
     * @return Map, где ключ - идентификатор продукта (Long), значение - список заказов на покупку (List&lt;PurchaseOrder&gt;),
     *         которые содержат данный продукт. Если для какого-либо productId не найдено соответствующих заказов,
     *         этот productId не будет присутствовать в результирующей карте. Никогда не возвращает null
     * 
     * @throws NullPointerException если purchaseOrders или productIds равны null
     * 
     * @see PurchaseOrder
     * @see Shipment
     * @see Offer
     * @see ProposedOffer
     */
    private Map<Long, List<PurchaseOrder>> groupOrdersByProductId(List<PurchaseOrder> purchaseOrders, Set<Long> productIds) {
        return purchaseOrders.stream()
                .flatMap(order -> safeStream(order.getShipments())
                        .flatMap(shipment -> safeStream(shipment.getOffers())
                                .flatMap(offer -> {
                                    if (offer.getProductId() != null && productIds.contains(offer.getProductId())) {
                                        return Stream.of(Map.entry(offer.getProductId(), order));
                                    }
                                    return safeStream(offer.getProposedOffers())
                                            .filter(proposedOffer -> proposedOffer.getProposedProductId() != null && productIds.contains(proposedOffer.getProposedProductId()))
                                            .map(proposedOffer -> Map.entry(proposedOffer.getProposedProductId(), order));
                                })
                        )
                )
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }

    private boolean processOrder(PurchaseOrder order, Long productId, OrderInfoDTO orderInfo, boolean isCustomerMatch, List<OfferPairDTO> offerPairs) {
        boolean changed = false;
        if (isCustomerMatch) {
            changed = addOrderIdIfMissing(order, orderInfo.orderID());
        }
        changed |= processShipments(order.getShipments(), productId, orderInfo.orderID(), orderInfo.clientId(), isCustomerMatch, offerPairs);
        return changed;
    }

    private boolean processShipments(Set<Shipment> shipments, Long productId, Long orderId, Long clientId, boolean isCustomerMatch, List<OfferPairDTO> offerPairs) {
        boolean changed = false;
        for (Shipment shipment : safeIterable(shipments)) {
            changed |= processOffers(shipment.getOffers(), productId, orderId, clientId, isCustomerMatch, offerPairs);
        }
        return changed;
    }

    private boolean processOffers(Set<Offer> offers, Long productId, Long orderId, Long clientId, boolean isCustomerMatch, List<OfferPairDTO> offerPairs) {
        boolean changed = false;
        for (Offer offer : safeIterable(offers)) {
            changed |= processOffer(offer, productId, orderId, clientId, isCustomerMatch, offerPairs);
        }
        return changed;
    }

    private boolean processOffer(Offer offer, Long productId, Long orderId, Long clientId, boolean isCustomerMatch, List<OfferPairDTO> offerPairs) {
        boolean changed = false;
        if (Objects.equals(offer.getProductId(), productId)) {
            changed = updateOfferOrderIdOrMissed(offer, orderId, isCustomerMatch);
            addOfferPairIfSentToCustomer(offer, offerPairs);
        }
        changed |= processProposedOffers(offer.getProposedOffers(), productId, orderId, clientId, isCustomerMatch, offerPairs);
        return changed;
    }

    private boolean processProposedOffers(Set<ProposedOffer> proposedOffers, Long productId, Long orderId, Long clientId, boolean isCustomerMatch, List<OfferPairDTO> offerPairs) {
        boolean changed = false;
        for (ProposedOffer proposedOffer : safeIterable(proposedOffers)) {
            if (Objects.equals(proposedOffer.getProposedProductId(), productId)) {
                if (isCustomerMatch) {
                    changed |= updateProposedOfferOrderId(proposedOffer, orderId);
                } else {
                    changed |= markOfferAsMissed(proposedOffer.getOffer());
                }
                addOfferPairIfSentToCustomer(proposedOffer.getOffer(), offerPairs);
            }
        }
        return changed;
    }

    private boolean updateOfferOrderIdOrMissed(Offer offer, Long orderId, boolean isCustomerMatch) {
        if (isCustomerMatch) {
            return updateOrderIdIfChanged(offer::getOrderId, offer::setOrderId, orderId);
        } else {
            return markOfferAsMissed(offer);
        }
    }

    private boolean markOfferAsMissed(Offer offer) {
        if (offer.getPurchasePlatformStatus() != PurchasePlatformStatus.MISSED) {
            offer.setPurchasePlatformStatus(PurchasePlatformStatus.MISSED);
            log.debug("Оффер с ID={} помечен как упущенный", offer.getId());
            return true;
        }
        return false;
    }

    private void addOfferPairIfSentToCustomer(Offer offer, List<OfferPairDTO> offerPairs) {
        if (Boolean.TRUE.equals(offer.getIsSentToCustomer()) && offer.getBitrixId() != null && offer.getOrderId() != null) {
            offerPairs.add(new OfferPairDTO(offer.getBitrixId(), offer.getOrderId()));
            log.debug("Добавлена пара [bitrixId={}, orderId={}] для оффера с ID={}", offer.getBitrixId(), offer.getOrderId(), offer.getId());
        }
    }

    private boolean updateOrderIdIfChanged(java.util.function.Supplier<Long> getter, java.util.function.Consumer<Long> setter, Long newOrderId) {
        if (!Objects.equals(getter.get(), newOrderId)) {
            setter.accept(newOrderId);
            return true;
        }
        return false;
    }

    /**
     * Проверяет корректность входных данных для обработки заказа.
     *
     * @param orderInfo информация о заказе
     * @throws ValidationException если входные данные некорректны
     */
    private void validateOrderInfo(OrderInfoDTO orderInfo) {
        if (orderInfo == null) {
            throw new ValidationException("OrderInfoDTO не должен быть null", null, null, null, 400);
        }
        if (CollectionUtils.isEmpty(orderInfo.productIds())) {
            throw new ValidationException("Список productIds не должен быть null или пустым", null, null, null, 400);
        }
        if (orderInfo.clientId() == null) {
            throw new ValidationException("ClientId не должен быть null", null, null, null, 400);
        }
        if (orderInfo.orderID() == null) {
            throw new ValidationException("OrderID не должен быть null", null, null, null, 400);
        }
    }

    /**
     * Обновляет идентификатор заказа в предложенном оффере, если он отличается от текущего.
     *
     * @param proposedOffer предложенный оффер для обновления
     * @param orderId       новый идентификатор заказа
     * @return true, если изменение было внесено, иначе false
     */
    private boolean updateProposedOfferOrderId(ProposedOffer proposedOffer, Long orderId) {
        return updateOrderIdIfChanged(proposedOffer::getOrderId, proposedOffer::setOrderId, orderId);
    }

    private void processOrderStatusTransition(PurchaseOrder order) {
        if (PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER.equals(order.getStatus())) {
            processOrderStatusTransitionInNewTransaction(order.getId());
        }
    }

    @Transactional
    public void processOrderStatusTransitionInNewTransaction(Long orderId) {
        purchaseOrderService.processOrderStatusTransitionInNewTransaction(orderId);
    }

    // Универсальные методы для безопасной работы с коллекциями
    private static <T> Stream<T> safeStream(Iterable<T> iterable) {
        return iterable == null ? Stream.empty() : StreamSupport.stream(iterable.spliterator(), false);
    }
    private static <T> Iterable<T> safeIterable(Iterable<T> iterable) {
        return iterable == null ? List.of() : iterable;
    }

    private boolean addOrderIdIfMissing(PurchaseOrder order, Long orderId) {
        if (order.getOrders() == null) {
            order.setOrders(new HashSet<>());
        }
        if (!order.getOrders().contains(orderId)) {
            order.getOrders().add(orderId);
            return true;
        }
        return false;
    }
}