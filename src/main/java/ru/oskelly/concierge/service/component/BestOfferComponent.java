package ru.oskelly.concierge.service.component;

import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.service.dto.BestOfferDTO;

import java.util.List;

/**
 * Компонент для получения лучших предложений
 */
public interface BestOfferComponent {
    /**
     * Получить лучшие предложения
     *
     * @param shipment товар, для которого нужно получить лучшие предложения
     * @return лучшие предложения
     */
    List<BestOfferDTO> findBestOffers(Shipment shipment);
    /**
     * Получить лучшие предложения
     *
     * @param shipmentId идентификатор товара, для которого нужно получить лучшие предложения
     * @return лучшие предложения
     */
    List<BestOfferDTO> findBestOffers(Long shipmentId);

}
