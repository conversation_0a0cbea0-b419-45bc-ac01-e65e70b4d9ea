package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.bitrix.client.BitrixClient;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseRequest;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.BitrixContactDTO;
import ru.oskelly.concierge.exception.BitrixClientException;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BitrixServiceImpl implements BitrixService {

    private final BitrixClient bitrixClient;

    @Override
    public BitrixBaseResponse<Long> createDeal(BitrixBaseRequest request) {
        BitrixBaseResponse<Long> response;
        try {
            log.info("Создание сделки в Битрикс...");
            response = bitrixClient.createDeal(request);
            log.info("Сделка успешно создана: {}", response.getResult());
        } catch (BitrixClientException exception) {
            throw new BitrixClientException("Ошибка при создании сделки в Битрикс");
        }

        return response;
    }

    @Override
    public BitrixBaseResponse<List<BitrixContactDTO>> listContacts(BitrixBaseRequest request) {
        BitrixBaseResponse<List<BitrixContactDTO>> response;
        try {
            log.info("Получение контакта в Битрикс по номеру телефона...");
            response = bitrixClient.listContacts(request);
        } catch (BitrixClientException exception) {
            throw new BitrixClientException("Ошибка при попытке получить контакт в Битрикс");
        }

        return response;
    }

    @Override
    public BitrixBaseResponse<Long> createContact(BitrixBaseRequest request) {
        BitrixBaseResponse<Long> response;
        try {
            log.info("Создание контакта в Битрикс...");
            response = bitrixClient.createContact(request);
            log.info("Контакт успешно создан: {}", response.getResult());
        } catch (BitrixClientException exception) {
            throw new BitrixClientException("Ошибка при создании контакта в Битрикс");
        }

        return response;
    }

}
