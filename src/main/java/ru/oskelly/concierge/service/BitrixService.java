package ru.oskelly.concierge.service;

import ru.oskelly.concierge.bitrix.dto.BitrixBaseRequest;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.BitrixContactDTO;

import java.util.List;

public interface BitrixService {

    BitrixBaseResponse<Long> createDeal(BitrixBaseRequest request);

    BitrixBaseResponse<List<BitrixContactDTO>> listContacts(BitrixBaseRequest request);

    BitrixBaseResponse<Long> createContact(BitrixBaseRequest request);
}
