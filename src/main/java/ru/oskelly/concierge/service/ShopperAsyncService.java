package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.bitrix.client.BitrixClient;
import ru.oskelly.concierge.bitrix.dto.BitrixBaseResponse;
import ru.oskelly.concierge.bitrix.dto.ShopperListRequest;
import ru.oskelly.concierge.bitrix.dto.ShopperResponse;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ShopperAsyncService {

    /**
     * Все стандартные поля (без пользовательских)
     */
    public static final String SELECT_ALL_FIELDS = "*";

    /**
     * Все пользовательские поля
     */
    public static final String SELECT_USER_FIELDS = "UF_*";

    private final BitrixClient bitrixClient;
    private final PersonalShopperImportService shopperImportService;

    /**
     * Для получения следующего пакета элементов,
     * необходимо выполнить тот же самый запрос,
     * указав дополнительный параметр start
     * со значением, пришедшем в параметре next ответа.
     */
    @Async
    public void executePull() {
        List<ShopperResponse> shoppers = new ArrayList<>();
        Integer start = 0;

        try {
            while (start != null) {
                var request = buildShopperListRequest(start);

                BitrixBaseResponse<List<ShopperResponse>> response = bitrixClient.getShoppers(request);
                List<ShopperResponse> page = response.getResult();
                log.info("Получено {} шопперов (start={})", page.size(), start);
                shoppers.addAll(page);
                start = response.getNext();
            }

            log.info("Всего получено {} шопперов", shoppers.size());
            shopperImportService.importShoppers(shoppers);
        } catch (Exception e) {
            log.error("Ошибка при получении шопперов", e);
        }
    }

    private ShopperListRequest buildShopperListRequest(Integer start) {
        return ShopperListRequest.builder()
                .start(start)
                .select(List.of(SELECT_ALL_FIELDS, SELECT_USER_FIELDS))
                .build();
    }

}
