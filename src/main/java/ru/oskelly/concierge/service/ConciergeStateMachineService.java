package ru.oskelly.concierge.service;

import org.springframework.statemachine.StateMachine;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.util.Map;

/**
 * Сервис работы с машиной состояний
 */
public interface ConciergeStateMachineService {

    /**
     * Запуск машины состояний
     * @param purchaseOrderId идентификатор заявки
     * @param variables переменные окружения
     */
    void startStateMachine(Long purchaseOrderId, Map<String, Object> variables);

    /**
     * Отправка события в машину состояний c получением машины отдельно
     * @param stateMachine  - экземпляр машины
     * @param event         - событие для машины
     */
    void sendEvent(StateMachine<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachine,
                   PurchaseOrderTransitionEvent event);

    /**
     * Отправка события в машину состояний
     * @param purchaseOrder - заявка
     * @param event         - событие для машины
     */
    void sendEvent(PurchaseOrder purchaseOrder, PurchaseOrderTransitionEvent event);

    /**
     * Получение экземпляра машины состояний по идентификатору заявки
     * @param orderId - идентификатор заявки
     * @return - экземпляр машины состояний
     */
    StateMachine<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> getStateMachine(Long orderId);
}
