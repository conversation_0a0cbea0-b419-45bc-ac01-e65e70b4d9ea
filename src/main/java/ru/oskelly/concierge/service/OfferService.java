package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.SendOffersToClientRequest;
import ru.oskelly.concierge.controller.dto.ShipmentOffersDTO;

import java.util.List;

public interface OfferService {
    /**
     * Добавление товара
     * @param shipmentOffers - DTO с данными товара
     * @return - DTO с данными предложения
     */
    ShipmentOffersDTO addingProducts(ShipmentOffersDTO shipmentOffers);

    /**
     * Добавление шоперов
     * @param shipmentOffers - DTO с данными шоперов
     * @return - DTO с данными предложения
     */
    ShipmentOffersDTO addingShoppers(ShipmentOffersDTO shipmentOffers);

    /**
     * Удаление товара
     * @param offerId - идентификатор товара
     * @return - пустой ответ
     */
    Void deleteProduct(Long offerId);

    /**
     * Добавление предложения шопера
     * @param shipmentOffers - набор предложений
     * @return - набор обновленных данных
     */
    ShipmentOffersDTO addShopperOffer(ShipmentOffersDTO shipmentOffers);

    /**
     * Отправка предложений клиенту в WhatsApp
     * @param request предложения для отправки
     */
    void sendOffersToClient(SendOffersToClientRequest request);

    /**
     * Установка признака отправки предложений клиенту
     * @param offerIds идентификаторы предложений
     */
    void setOffersAsSentToCustomer(List<Long> offerIds, List<Long> proposedOfferIds);
}
