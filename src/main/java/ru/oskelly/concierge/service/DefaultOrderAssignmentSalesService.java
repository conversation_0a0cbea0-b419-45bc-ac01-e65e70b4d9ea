package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.kafka.dto.AssignClientToSalesMessage;
import ru.oskelly.concierge.kafka.dto.RequestAvailableSalesMessage;
import ru.oskelly.concierge.kafka.producer.KafkaMessageProducer;

import java.util.List;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultOrderAssignmentSalesService implements OrderAssignmentSalesService {
    private final PurchaseOrderService purchaseOrderService;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final KafkaMessageProducer kafkaMessageProducer;

    @Value("${kafka.topics.sales-requests-available-topic.name}")
    private String salesRequestsAvailableTopic;

    @Value("${kafka.topics.sales-assignments-client-topic.name}")
    private String salesAssignmentsClientTopic;

    @Override
    public void requestAvailableSales(long orderId) {
        kafkaMessageProducer.sendMessage(
            salesRequestsAvailableTopic,
            new RequestAvailableSalesMessage(orderId, Roles.SALES)
        );
    }

    @Override
    public void requestAvailableSales(List<Long> orderIds) {
        List<RequestAvailableSalesMessage> messages = orderIds
            .stream()
            .map(id -> new RequestAvailableSalesMessage(id, Roles.SALES))
            .toList();

        kafkaMessageProducer.sendBatchMessages(salesRequestsAvailableTopic, messages);
    }

    @Override
    public void assignToSales(long orderId, SalesInfoDTO salesInfo) {
        purchaseOrderService.assignSales(orderId, salesInfo);

        PurchaseOrder purchaseOrderEntity = purchaseOrderService.getPurchaseOrderEntity(orderId, null);

        kafkaMessageProducer.sendMessage(
            salesAssignmentsClientTopic,
            new AssignClientToSalesMessage(
                purchaseOrderEntity.getCustomerId(),
                salesInfo.salesId()
            )
        );
    }

    @Override
    public void redistribute(long fromSalesId, SalesInfoDTO toSalesInfo) {
        List<Long> orderIds = getSalesOrderIds(fromSalesId);

        if (toSalesInfo == null) {
            requestAvailableSales(orderIds);
            return;
        }

        purchaseOrderService.assignSales(orderIds, toSalesInfo);
    }

    /**
     * Получение заявок Сейлза в определенных статусах
     * @param salesId идентификатор Сейлза
     * @return список идентификаторов заявок
     */
    private List<Long> getSalesOrderIds(long salesId) {
        Set<PurchaseOrderStatusEnum> reassignableStatuses = Set.of(
            PurchaseOrderStatusEnum.NEW,
            PurchaseOrderStatusEnum.IN_PROGRESS_SALES,
            PurchaseOrderStatusEnum.AWAITING_SOURCER,
            PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER,
            PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT
        );

        return purchaseOrderRepository.findBySalesIdAndStatusIn(salesId, reassignableStatuses);
    }
}
