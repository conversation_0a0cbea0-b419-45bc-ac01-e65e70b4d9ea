package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.controller.dto.SalesManagerProductDto;
import ru.oskelly.concierge.controller.dto.SellerInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.enums.TypeSeller;
import ru.oskelly.concierge.service.component.BestOfferComponent;
import ru.oskelly.concierge.service.dto.BestOfferDTO;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;
import ru.oskelly.concierge.service.dto.OfferType;

import java.util.List;

@Service
@RequiredArgsConstructor
public class DefaultSalesManagerService implements SalesManagerService {
    private final BestOfferComponent bestOfferComponent;
    @Override
    public List<SalesManagerProductDto> getSalesOrders(Long shipmentId) {
        List<BestOfferDTO> bestOffers = bestOfferComponent.findBestOffers(shipmentId);
        return bestOffers.stream()
                .map(this::mapToSalesManagerProductDto)
                .toList();
    }

    private SalesManagerProductDto mapToSalesManagerProductDto(BestOfferDTO bestOffer) {
        if (bestOffer.getType() == OfferType.OFFER) {
            Offer offer = bestOffer.getOffer();
            return new SalesManagerProductDto(
                    offer.getProductId(),
                    new SellerInfoDTO(offer.getSellerId(), null, null, null, null, null),
                    TypeSeller.PRODUCT_PLATFORM,
                    null,
                    null,
                    null,
                    bestOffer.getCriteria().contains(ComparisonCriterion.PRICE),
                    bestOffer.getCriteria().contains(ComparisonCriterion.DELIVERY_DATE)
            );
        } else {
            ProposedOffer proposedOffer = bestOffer.getProposedOffer();
            return new SalesManagerProductDto(
                    null, // productId - нет в ProposedOffer
                    new SellerInfoDTO(proposedOffer.getOffer().getSellerId(), null, null, null, null, null),
                    TypeSeller.BAYER,
                    proposedOffer.getRublePrice(),
                    proposedOffer.getDeliveryDate(),
                    proposedOffer.getValidUntil(),
                    bestOffer.getCriteria().contains(ComparisonCriterion.PRICE),
                    bestOffer.getCriteria().contains(ComparisonCriterion.DELIVERY_DATE)
            );
        }
    }
}
