package ru.oskelly.concierge.service.dto;


import io.swagger.v3.oas.annotations.media.Schema;

public enum ComparisonCriterion {

    @Schema(description = "Лучшая цена")
    PRICE("Лучшая цена"),

    @Schema(description = "Лучшая доставка")
    DELIVERY_DATE("Лучшая доставка");

    private final String description;

    ComparisonCriterion(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
