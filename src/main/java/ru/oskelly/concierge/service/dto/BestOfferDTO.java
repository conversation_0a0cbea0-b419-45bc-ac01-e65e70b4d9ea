package ru.oskelly.concierge.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;

import java.util.Set;

/**
 * Внутренний DTO для представления лучшего предложения
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BestOfferDTO {
    /**
     * Тип предложения(товар или предложение)
     */
    private OfferType type;
    /**
     * Идентификатор товара с платформы или идентификатор предложения
     */
    private Offer offer;
    /**
     * Предложение
     */
    private ProposedOffer proposedOffer;
    /**
     * Критерии сравнения (могут быть оба критерия)
     */
    private Set<ComparisonCriterion> criteria;

    public void setOffer(Offer offer) {
        this.offer = offer;
        updateType();
    }

    public void setProposedOffer(ProposedOffer proposedOffer) {
        this.proposedOffer = proposedOffer;
        updateType();
    }

    private void updateType() {
        if (offer != null) {
            this.type = OfferType.OFFER;
        } else if (proposedOffer != null) {
            this.type = OfferType.PROPOSED_OFFER;
        }
    }

}
