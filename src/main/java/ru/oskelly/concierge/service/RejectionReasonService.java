package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.RejectionEventRequest;
import ru.oskelly.concierge.data.model.RejectionReason;
import ru.oskelly.concierge.data.model.enums.ObjectType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.List;

/**
 * Сервис для работы с причинами отклонений
 */
public interface RejectionReasonService {
    /**
     * Получение списка причин отклонения
     *
     * @param userId
     * @param objectType тип объекта (обязательный)
     * @param status     заявки (только для типа ORDER)
     * @return отфильтрованный список причин
     */
    List<RejectionReason> getRejectionReasons(Long userId, ObjectType objectType, PurchaseOrderStatusEnum status);
    /**
     * Создать событие отклонения заявки
     *
     * @param request - запрос на отклонение
     * @param orderId - идентификатор заявки
     */
    PurchaseOrderFullDTO createRejectionEvent(RejectionEventRequest request, Long orderId);
    /**
     * Вернуть отклоненную заявку в работу
     * @param userId - идентификатор пользователя
     * @param orderId - идентификатор заявки
     * @param comment - комментарии с причиной возврата
     */
    PurchaseOrderFullDTO returnToWork(Long userId, Long orderId, String comment);
}
