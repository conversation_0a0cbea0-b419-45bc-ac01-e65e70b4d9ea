package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.ShipmentCommentDTO;
import ru.oskelly.concierge.controller.dto.ShipmentRequestDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;

import java.util.List;

/**
 * Интерфейс сервиса для работы с заявками
 */
public interface ShipmentService {
    /**
     * Добавление товара в заявку
     *
     * @param orderId - идентификатор заявки
     * @param userId
     * @return - DTO с данными заявки
     */
    ShipmentResponseDTO addEmptyShipmentToOrder(Long orderId, Long userId);

    /**
     * Добавление нескольких товаров в заявку
     * @param shipments товары
     * @param orderId идентификатор заявки
     */
    List<ShipmentResponseDTO> addShipmentsToOrder(List<ShipmentRequestDTO> shipments, Long orderId);

    /**
     * Удаление заявки
     * @param shipmentId - идентификатор заявки
     */
    void deleteShipment(Long shipmentId);

    /**
     * Обновление заявки
     * @param shipmentId - идентификатор заявки
     * @param requestDTO - DTO с данными заявки
     * @return - DTO с данными обновленной заявки
     */
    ShipmentResponseDTO updateShipment(Long shipmentId, ShipmentRequestDTO requestDTO);

    /**
     * Добавление комментария к товару в заявке
     * @param comment - DTO с данными комментария
     * @return - DTO с данными обновленного товара
     * */
    ShipmentResponseDTO addCommentToShipment(ShipmentCommentDTO comment);
}
