package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.controller.dto.CommentChangeRequest;
import ru.oskelly.concierge.controller.dto.CommentFullDTO;
import ru.oskelly.concierge.controller.dto.CommentListDTO;
import ru.oskelly.concierge.data.mapper.CommentMapper;
import ru.oskelly.concierge.data.model.Comment;
import ru.oskelly.concierge.data.repository.CommentRepository;
import ru.oskelly.concierge.exception.CommentNotFoundException;

import java.time.ZonedDateTime;
import java.util.ArrayList;

@Service
@RequiredArgsConstructor
public class DefaultCommentService implements CommentService {
    private final CommentRepository commentRepository;
    private final PurchaseOrderService purchaseOrderService;
    private final CommentMapper commentMapper;

    @Override
    public CommentListDTO getComments(Long orderId) {
        if (orderId == null)
            return new CommentListDTO(new ArrayList<>());
        var purchaseOrder = purchaseOrderService.getPurchaseOrderEntity(orderId, null);
        var comments = commentRepository.findAllByPurchaseOrder(purchaseOrder);
        return new CommentListDTO(comments.stream().map(commentMapper::toDto).toList());
    }

    @Override
    public CommentFullDTO addNewComment(Long orderId, CommentChangeRequest createRequest) {
        var purchaseOrder = purchaseOrderService.getPurchaseOrderEntity(orderId, null);
        var comment = Comment.builder()
                .authorId(createRequest.authorId())
                .message(createRequest.message())
                .datetime(ZonedDateTime.now())
                .purchaseOrder(purchaseOrder).build();
        comment = commentRepository.save(comment);
        return commentMapper.toDto(comment);
    }

    @Override
    public CommentFullDTO getComment(Long orderId, Long id) {
        return commentMapper.toDto(getCommentEntity(id, orderId));
    }

    @Override
    public void deleteComment(Long orderId, Long id) {
        var comment = getCommentEntity(id, orderId, false);
        if (comment != null) {
            commentRepository.deleteById(id);
            commentRepository.delete(comment);
        }
    }

    @Override
    public CommentFullDTO pinComment(Long orderId, Long id) {
        var comment = commentRepository.findById(id)
                .orElseThrow(() -> new CommentNotFoundException(String.format("Не найден комментарий с ID %s", id), null, orderId, 404));
        if (!comment.getPurchaseOrder().getId().equals(orderId)) {
            throw new CommentNotFoundException("Комментарий с ID " + id + " не найдет в заявке с ID " + orderId, null, orderId, 404);
        }
        if (comment.getIsPined() == null) {
            comment.setIsPined(true);
        } else {
            comment.setIsPined(!comment.getIsPined());
        }
        return commentMapper.toDto(commentRepository.save(comment));
    }

    @Override
    public CommentFullDTO editComment(Long orderId, Long id, CommentChangeRequest changeRequest) {
        var comment = getCommentEntity(id, orderId);
        comment.setAuthorId(changeRequest.authorId());
        comment.setMessage(changeRequest.message());
        comment.setDatetime(ZonedDateTime.now());
        return commentMapper.toDto(commentRepository.save(comment));
    }

    private Comment getCommentEntity(Long id, Long orderId, boolean throwException) {
        var comment = commentRepository.findByIdAndPurchaseOrder_Id(id, orderId);
        if (comment.isEmpty() && throwException) {
            throw new CommentNotFoundException("Не найден комментарий с ID " + id + " в заявке с ID " + orderId, null, orderId, 404);
        }
        return comment.orElse(null);
    }

    private Comment getCommentEntity(Long id, Long orderId) {
        return getCommentEntity(id, orderId, true);
    }
}
