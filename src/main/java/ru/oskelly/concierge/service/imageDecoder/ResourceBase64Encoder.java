package ru.oskelly.concierge.service.imageDecoder;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;

@Component
@RequiredArgsConstructor
@Slf4j
public class ResourceBase64Encoder {

    private final RestTemplate restTemplate;

    /**
     * Получает ресурс по ссылке и кодирует его в Base64
     *
     * @param url ссылка на ресурс
     */
    public String getBase64StringByUrl(String url) {
        log.info("URL " + url);
        return restTemplate.execute(url, HttpMethod.GET, null,
                response -> Base64.getEncoder().encodeToString(StreamUtils.copyToByteArray(response.getBody()))
        );
    }

    public String getBase64DataUriStringByUrl(String url) {
        return restTemplate.execute(url, HttpMethod.GET, null, this::mapResponseToBase64DataUrlString);
    }

    @SneakyThrows
    private String mapResponseToBase64DataUrlString(ClientHttpResponse clientHttpResponse) {
        String contentType = clientHttpResponse.getHeaders().getContentType().toString();
        String base64Content = Base64.getEncoder().encodeToString(StreamUtils.copyToByteArray(clientHttpResponse.getBody()));
        if (StringUtils.isEmpty(contentType)) {
            return base64Content;
        }

        return String.format("data:%s;base64,%s", contentType, base64Content);
    }
}
