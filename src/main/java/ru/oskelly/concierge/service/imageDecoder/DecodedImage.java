package ru.oskelly.concierge.service.imageDecoder;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Data
public class DecodedImage implements MultipartFile {
    private String extension;
    private byte[] bytes;
    private String id;
    private boolean fakeExtension;

    public DecodedImage(String extension, byte[] bytes, boolean fakeExtension) {
        this.extension = extension;
        this.bytes = bytes;
        this.id = UUID.randomUUID().toString();
        this.fakeExtension = fakeExtension;
    }

    @Override
    public String getName() {
        return this.id + "." + this.id;
    }

    @Override
    public String getOriginalFilename() {
        return fakeExtension ? getName() : this.id + "." + this.extension;
    }

    @Override
    public String getContentType() {
        return "image/" + extension;
    }

    @Override
    public boolean isEmpty() {
        return bytes.length == 0;
    }

    @Override
    public long getSize() {
        return bytes.length;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public void transferTo(File file) throws IOException, IllegalStateException {
        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            outputStream.write(bytes);
        }
    }
}
