package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperCreateRequest;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;
import ru.oskelly.concierge.data.model.PersonalShopper;

public interface PersonalShopperService {
    /**
     * Создание шопера
     * @param request - параметры шопера
     * @return - созданный шопер
     */
    PersonalShopper createPersonalShopper(PersonalShopperCreateRequest request);

    /**
     * Фильтр для шоперов
     * @return - фильтр шоперов
     */
    PersonalShopperFilter filterPersonalShopper();

    /**
     * Получение списка шоперов
     * @param filter - фильтр шоперов
     * @param searchText - текст поиска
     * @return - список шоперов
     */
    PaginatedShoppersResult getPersonalShoppers(PersonalShopperFilter filter, String searchText);
}
