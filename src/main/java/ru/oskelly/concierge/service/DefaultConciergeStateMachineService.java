package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.StateMachineException;
import org.springframework.statemachine.service.StateMachineService;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.exception.StateMachineInitializationException;
import ru.oskelly.concierge.exception.StateMachineInvalidTransitionException;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultConciergeStateMachineService implements ConciergeStateMachineService {
    private final StateMachineService<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachineService;

    @Override
    public void startStateMachine(Long purchaseOrderId, Map<String, Object> variables) {
        try {
            StateMachine<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachine = stateMachineService
                .acquireStateMachine(
                    purchaseOrderId.toString(), false
                );

            stateMachine.getExtendedState().getVariables().putAll(variables);
            ThreadLocalContext.put(ContextConstants.STATE_MACHINE_ID, purchaseOrderId);

            stateMachine.start();

            stateMachineService.releaseStateMachine(purchaseOrderId.toString());
            log.debug("StateMachine запущена для order ID: {}", purchaseOrderId);
        } catch (StateMachineException e) {
            throw new StateMachineInitializationException(
                "Ошибка инициализации StateMachine для order ID: " + purchaseOrderId, e, purchaseOrderId, 500
            );
        } finally {
            ThreadLocalContext.clean();
        }
    }

    @Override
    public void sendEvent(StateMachine<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachine, PurchaseOrderTransitionEvent event) {
        ThreadLocalContext.put(ContextConstants.STATE_MACHINE_ID, Long.parseLong(stateMachine.getId()));
        try {
            stateMachine.sendEvent(Mono.just(MessageBuilder.withPayload(event).build())).blockLast();
            // Проверка на ошибку
            if (ThreadLocalContext.get(ContextConstants.STATE_MACHINE_ERROR, Boolean.class) == Boolean.TRUE) {
                String errorMessage = ThreadLocalContext.get(ContextConstants.STATE_MACHINE_ERROR_MESSAGE,
                        String.class);
                throw new StateMachineInvalidTransitionException(errorMessage,
                        null,
                        Long.getLong(stateMachine.getId()),
                        400);
            }
        } finally {
            ThreadLocalContext.clean();
        }
    }

    @Override
    public void sendEvent(PurchaseOrder purchaseOrder, PurchaseOrderTransitionEvent event) {
        var stateMachine = getStateMachine(purchaseOrder.getId());
        sendEvent(stateMachine, event);
    }

    @Override
    public StateMachine<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> getStateMachine(Long orderId) {
        return stateMachineService.acquireStateMachine(orderId.toString());
    }
}
