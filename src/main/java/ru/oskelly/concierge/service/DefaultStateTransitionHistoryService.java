package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;
import ru.oskelly.concierge.data.mapper.PurchaseOrderStateHistoryMapper;
import ru.oskelly.concierge.data.model.PurchaseOrderStateHistory;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.data.repository.PurchaseOrderStateHistoryRepository;
import ru.oskelly.concierge.exception.PurchaseOrderNotFoundException;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultStateTransitionHistoryService implements StateTransitionHistoryService {
    private final PurchaseOrderStateHistoryRepository historyRepository;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final PurchaseOrderStateHistoryMapper purchaseOrderStateHistoryMapper;
    private final PurchaseOrderStateHistoryRepository purchaseOrderStateHistoryRepository;

    @Override
    public List<PurchaseOrderStateHistoryDTO> getTransitionHistory(Long purchaseOrderId) {
        if (!purchaseOrderRepository.existsById(purchaseOrderId)) {
            throw new PurchaseOrderNotFoundException(String.format("Заявка с ID %s не найдена", purchaseOrderId), null, purchaseOrderId, 404);
        }

        List<PurchaseOrderStateHistory> history = historyRepository
                .findByPurchaseOrderIdOrderByTransitionDateDesc(purchaseOrderId);

        return history.stream()
                .map(purchaseOrderStateHistoryMapper::toDto)
                .toList();
    }

    @Override
    public boolean checkStatusPassed(Long purchaseOrderId, PurchaseOrderStatusEnum status) {
        return purchaseOrderStateHistoryRepository.existsByPurchaseOrderIdAndTargetState(purchaseOrderId, status);
    }
}
