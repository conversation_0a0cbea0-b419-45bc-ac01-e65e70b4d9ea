package ru.oskelly.concierge.service;

import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.controller.dto.OfferPairDTO;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.OrderSources;
import ru.oskelly.concierge.controller.dto.PaginatedResult;
import ru.oskelly.concierge.controller.dto.PurchaseOrderCreateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFilter;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderUpdateRequest;
import ru.oskelly.concierge.controller.dto.PurchaseOrderWithShipmentsCreateRequest;
import ru.oskelly.concierge.controller.dto.RequestsFilter;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.util.List;

/**
 * Сервис работы с заявками на покупку
 */
public interface PurchaseOrderService {

    /**
     * Создание заявки на покупку.
     * Так же стартует бизнес-процесс обработки заявки.
     *
     * @param request - запрос на создание заявки
     * @return DTO с данными по заявки
     */
    PurchaseOrderFullDTO createPurchaseOrder(PurchaseOrderCreateRequest request);

    /**
     * Создание заявки на покупку со списком товаров
     * Так же стартует бизнес-процесс обработки заявки.
     *
     * @param request - запрос на создание заявки
     * @return DTO с данными по заявки
     */
    PurchaseOrderFullDTO createPurchaseOrderWithShipments(PurchaseOrderWithShipmentsCreateRequest request);

    /**
     * Обработка событий для заявки
     *
     * @param orderId - id заявки
     * @param event   - обрабатываемое событие
     * @param role
     * @return DTO с обновленными данными заявки
     */
    PurchaseOrderFullDTO eventProcessing(Long orderId,
                                         PurchaseOrderTransitionEvent event,
                                         @NotNull Roles role);
    /**
     * Получение заявки на покупку по ID
     *
     * @param orderId         - ID заявки
     * @param customerId - ID пользователя
     * @param role - роль пользователя, выполняющего запрос
     * @return DTO с данными по заявке
     */
    PurchaseOrderFullDTO getPurchaseOrder(Long orderId, Roles role, Long customerId);

    /**
     * Получение сущности заявки на покупку по ID
     *
     * @param id         - ID заявки
     * @param customerId - ID пользователя
     * @return сущность заявки
     */
    PurchaseOrder getPurchaseOrderEntity(Long id, Long customerId);

    /**
     * Получение сущности заявки на покупку по ID
     *
     * @param id         - ID заявки
     * @param customerId - ID пользователя
     * @param withOffers - флаг, указывающий, нужно ли загружать предложения по товарам в заявке
     * @return сущность заявки
     */
    PurchaseOrder getPurchaseOrderEntity(Long id, Long customerId, boolean withOffers);

    /**
     * Обновление заявки пользователем из приложения
     * Можно обновить только свою заявку
     * Можно обновить только заявку в статусе DRAFT
     *
     * @param orderId    - ID заявки
     * @param userId     - ID пользователя
     * @param updateRequest - данные для обновления заявки
     * @return DTO с обновленными данными заявки
     */
    PurchaseOrderFullDTO updatePurchaseOrder(Long orderId, Long userId, PurchaseOrderUpdateRequest updateRequest);

    /**
     * Возвращаем количество заявок по статусам
     *
     * @return - количество заявок по статусам
     */
    PurchaseOrderFilter getPurchaseOrderStatusCountsAndStatuses(PurchaseOrderFilter filter, Roles role);

    /**
     * Получение списка заявок по фильтру
     * @param filter - фильтр
     * @return - список заявок
     */
    PaginatedResult getPurchaseOrders(RequestsFilter filter, String searchText, Roles role);

    /**
     * Получение источников заявок
     * @return - список источников
     */
    OrderSources getSources();

    /**
     * Назначить заявку на Сейлза
     * @param orderId идентификатор заявки
     * @param sales данные Сейлза
     */
    void assignSales(Long orderId, SalesInfoDTO sales);

    /**
     * Назначить заявки на Сейлза
     * @param orderIds идентификаторы заявок
     * @param sales данные Сейлза
     */
    void assignSales(List<Long> orderIds, SalesInfoDTO sales);

    /**
     * Назначить заявку на Сейлза
     * @param orderId идентификатор заявки
     * @param sourcer данные Сорсера
     */
    void assignSourcer(Long orderId, SourcerInfoDTO sourcer);

    /**
     * Назначить заявки на Сорсера
     * @param orderIds идентификаторы заявок
     * @param sourcer данные Сорсера
     */
    void assignSourcer(List<Long> orderIds, SourcerInfoDTO sourcer);

    /**
     * Обработка информации о заказе
     * - Ищет все заявки в статусе AWAITING_CLIENT_ANSWER для указанного clientId
     * - В этих заявках ищет в привязанных offer и proposedOffer указанный productId
     * - Если найдено, добавляет заказ в orderId и в список orders в purchaseOrder
     * - Возвращает пары [bitrixId, orderId] для предложений с isSentToCustomer = true
     *
     * @param orderInfo - информация о заказе (productId, orderID, clientId)
     * @return список пар [bitrixId, orderId] для предложений, отправленных клиенту
     */
    List<OfferPairDTO> processOrderInfo(OrderInfoDTO orderInfo);

    /**
     * Обрабатывает переход статуса заказа в новой транзакции
     *
     * @param orderId - идентификатор заказа
     */
    void processOrderStatusTransitionInNewTransaction(Long orderId);
}
