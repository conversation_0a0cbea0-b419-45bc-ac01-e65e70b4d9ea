package ru.oskelly.concierge.service;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.concierge.bitrix.dto.ShopperResponse;
import ru.oskelly.concierge.data.mapper.PersonalShopperMapper;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PersonalShopperImportServiceImpl implements PersonalShopperImportService {

    private final PersonalShopperRepository shopperRepository;
    private final PersonalShopperMapper mapper;

    @Override
    @Transactional
    public void importShoppers(List<ShopperResponse> shoppers) {
        if (shoppers.isEmpty()) {
            log.info("Bitrix вернул пустой список. Нечего импортировать.");
            return;
        }

        Map<Long, ShopperResponse> validShoppers = shoppers.stream()
                .filter(dto -> dto.oskellyUserId() != null && !dto.oskellyUserId().isBlank())
                .filter(dto -> parseIdSafe(dto.oskellyUserId()) != null)
                .collect(Collectors.toMap(
                        dto -> parseIdSafe(dto.oskellyUserId()),
                        Function.identity()
                ));

        List<Long> ids = new ArrayList<>(validShoppers.keySet());
        Map<Long, PersonalShopper> shoppersByUserId = shopperRepository
                .findAllByUserIdIn(ids).stream()
                .collect(Collectors.toMap(
                        PersonalShopper::getUserId,
                        Function.identity()
                ));

        validShoppers.forEach((userId, dto) ->
                shoppersByUserId.compute(userId, (key, existing) -> {
                    if (existing == null) {
                        return mapper.createEntityFromBitrix(dto);
                    }
                    mapper.updateEntityFromBitrix(existing, dto);
                    return existing;
                })
        );

        log.info("Валидных шопперов к сохранению: " + shoppersByUserId.size());
        shopperRepository.saveAll(shoppersByUserId.values());
    }

    private Long parseIdSafe(String id) {
        try {
            return Long.valueOf(id);
        } catch (NumberFormatException ex) {
            log.warn("Некорректный id шоппера \"{}\"", id);
            return null;
        }
    }
}