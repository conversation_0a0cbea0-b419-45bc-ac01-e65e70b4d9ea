package ru.oskelly.concierge.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.ShipmentCommentDTO;
import ru.oskelly.concierge.controller.dto.ShipmentRequestDTO;
import ru.oskelly.concierge.controller.dto.ShipmentResponseDTO;
import ru.oskelly.concierge.data.mapper.ShipmentMapper;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.data.repository.ShipmentRepository;
import ru.oskelly.concierge.exception.ResourceNotFoundException;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.IN_PROGRESS_SALES;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.NEW;

@Service
@RequiredArgsConstructor
public class DefaultShipmentService implements ShipmentService {

    private final ShipmentRepository shipmentRepository;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final ShipmentMapper shipmentMapper;
    private final ConciergeStateMachineService conciergeStateMachineService;
    private final PersonalShopperRepository personalShopperRepository;

    @Override
    @Transactional
    public ShipmentResponseDTO addEmptyShipmentToOrder(Long orderId, Long userId) {
        PurchaseOrder order = purchaseOrderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Заявка с ID " + orderId + " не найдена", null, orderId, 404));

        Shipment shipment = Shipment.builder()
                .purchaseOrder(order)
                .creatorId(userId)
                .createdAt(ZonedDateTime.now())
                .build();

        Shipment savedShipment = shipmentRepository.save(shipment);
        return shipmentMapper.toResponseDTO(savedShipment, personalShopperRepository);
    }

    @Override
    @Transactional
    public List<ShipmentResponseDTO> addShipmentsToOrder(List<ShipmentRequestDTO> shipments, Long orderId) {
        PurchaseOrder order = purchaseOrderRepository
                .findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Заявка с ID " + orderId + " не найдена", null, orderId, 404
                ));

        var stateMachine = conciergeStateMachineService.getStateMachine(orderId);

        Long userId = ThreadLocalContext.get(ContextConstants.USER_ID, Long.class);

        if (userId != null) {
            stateMachine.getExtendedState().getVariables().put(ContextConstants.USER_ID, userId);
        }

        if (order.getSalesId() != null) {
            stateMachine.getExtendedState().getVariables().put("salesId", order.getSalesId());
        }

        if (order.getStatus() != IN_PROGRESS_SALES && order.getStatus() != NEW) {
            throw new ResourceNotFoundException(
                "Добавить товары к заявке можно только в статусах NEW и IN_PROGRESS_SALES", null, orderId, 404
            );
        }

        conciergeStateMachineService.sendEvent(order, PurchaseOrderTransitionEvent.SEND_TO_SOURCER);

        PurchaseOrderStatusEnum currentStatus = stateMachine.getState().getId();

        Set<Shipment> shipmentEntities = shipmentMapper.toEntityUpdateSet(shipments, order);
        order.getShipments().addAll(shipmentEntities);
        order.setStatus(currentStatus);

        purchaseOrderRepository.save(order);

        return shipmentMapper.toResponseDtoList(order.getShipments(), personalShopperRepository);
    }

    @Override
    @Transactional
    public void deleteShipment(Long shipmentId) {
        Shipment shipment = shipmentRepository.findById(shipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Товар с ID " + shipmentId + " не найден", null, shipmentId, 404));

        PurchaseOrder order = shipment.getPurchaseOrder();
        if (order.getStatus() != PurchaseOrderStatusEnum.IN_PROGRESS_SALES) {
            throw new ResourceNotFoundException("Товар можно удалить только тогда, когда заказ находится в статусе IN_PROGRESS_SALES.", null, shipmentId, 404);
        }

        shipmentRepository.delete(shipment);
    }

    @Override
    @Transactional
    public ShipmentResponseDTO updateShipment(Long shipmentId, ShipmentRequestDTO requestDTO) {
        Shipment shipment = shipmentRepository.findById(shipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Товар с ID " + shipmentId + " не найден", null, shipmentId, 404));

        shipment = shipmentRepository.save(shipmentMapper.toEntityUpdate(requestDTO, shipment));
        return shipmentMapper.toResponseDTO(shipment, personalShopperRepository);
    }

    @Override
    @Transactional
    public ShipmentResponseDTO addCommentToShipment(ShipmentCommentDTO comment) {
        Long shipmentId = comment.shipmentId();
        Shipment shipment = shipmentRepository.findById(shipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Товар с ID " + shipmentId + " не найден", null, shipmentId, 404));

        shipment.setComment(comment.comment());
        shipment = shipmentRepository.save(shipment);
        return shipmentMapper.toResponseDTO(shipment, personalShopperRepository);
    }
}
