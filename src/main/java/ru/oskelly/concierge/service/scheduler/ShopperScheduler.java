package ru.oskelly.concierge.service.scheduler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.bitrix.config.ShopperScheduleProperties;
import ru.oskelly.concierge.service.ShopperAsyncService;

@Service
@RequiredArgsConstructor
@Slf4j
public class ShopperScheduler {
    private final ShopperAsyncService asyncService;
    private final ShopperScheduleProperties properties;

    @Scheduled(
            cron = "#{@shopperScheduleProperties.cron}"
    )
    public void pullShoppers() {
        asyncService.executePull();
    }
}
