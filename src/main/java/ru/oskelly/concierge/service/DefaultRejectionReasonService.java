package ru.oskelly.concierge.service;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.RejectionEventRequest;
import ru.oskelly.concierge.data.mapper.PurchaseOrderMapper;
import ru.oskelly.concierge.data.model.RejectionReason;
import ru.oskelly.concierge.data.model.enums.ObjectType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;
import ru.oskelly.concierge.data.repository.PurchaseOrderRepository;
import ru.oskelly.concierge.data.repository.RejectionReasonRepository;
import ru.oskelly.concierge.exception.CommentNotFoundException;
import ru.oskelly.concierge.exception.PurchaseOrderNotFoundException;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

import java.util.List;

@Service
@RequiredArgsConstructor
public class DefaultRejectionReasonService implements RejectionReasonService {
    private final RejectionReasonRepository rejectionReasonRepository;
    private final PurchaseOrderRepository purchaseOrderRepository;
    private final ConciergeStateMachineService conciergeStateMachineService;
    private final PurchaseOrderMapper purchaseOrderMapper;
    private final PersonalShopperRepository personalShopperRepository;

    @Override
    public List<RejectionReason> getRejectionReasons(Long userId, ObjectType objectType, PurchaseOrderStatusEnum status) {
        if (objectType == ObjectType.PURCHASE_ORDER && status == PurchaseOrderStatusEnum.ALL) {
            return rejectionReasonRepository.findByObjectType(objectType);
        }
        if (objectType == ObjectType.PRODUCT) {
            return rejectionReasonRepository.findByObjectType(objectType);
        }
        return rejectionReasonRepository.findByObjectTypeAndStatus(objectType, status);
    }

    @Override
    @Transactional
    public PurchaseOrderFullDTO createRejectionEvent(RejectionEventRequest request, Long orderId) {
        var purchaseOrder = purchaseOrderRepository.findById(orderId).orElseThrow(
                () -> new PurchaseOrderNotFoundException(String.format("Заявка с ID %s не найдена", orderId), null, orderId, 404)
        );
        // Отклонение никак не связано с состоянием и статусом заявки
        conciergeStateMachineService.sendEvent(purchaseOrder, PurchaseOrderTransitionEvent.SEND_TO_REJECTION);
        purchaseOrder.setRejectionReason(request.getRejectionReason());
        purchaseOrder.setRejectionDescription(request.getAdditionalText());
        purchaseOrder.setStatus(PurchaseOrderStatusEnum.REJECTED);
        return purchaseOrderMapper.toFullDto(purchaseOrderRepository.save(purchaseOrder), personalShopperRepository);
    }

    @Override
    @Transactional
    public PurchaseOrderFullDTO returnToWork(Long userId, Long orderId, String comment) {
        var purchaseOrder = purchaseOrderRepository.findById(orderId).orElseThrow(
                () -> new PurchaseOrderNotFoundException(String.format("Заявка с ID %s не найдена", orderId), null, orderId, 404)
        );
        if (!purchaseOrder.getStatus().equals(PurchaseOrderStatusEnum.REJECTED)) {
            throw new PurchaseOrderNotFoundException(String.format("Заявка с ID %s не найдена и в статусе REJECTED не найдена", orderId), null, orderId, 404);
        }
        if (purchaseOrder.getSourcerId() != null){
            throw new PurchaseOrderNotFoundException(String.format("Заявка с ID %s не может быть возвращена в работу. Находится в неверном состоянии(SourcerId не пустой)", orderId), null, orderId, 400);
        }
        if (comment == null || comment.isEmpty()) {
            throw new CommentNotFoundException("Комментарий не может быть пустым", null, orderId, 404);
        }
        ThreadLocalContext.put(ContextConstants.REASON_RETURN, comment);
        conciergeStateMachineService.sendEvent(purchaseOrder, PurchaseOrderTransitionEvent.RESTORE_BY_SALES);
        purchaseOrder.setStatus(PurchaseOrderStatusEnum.IN_PROGRESS_SALES);
        purchaseOrder.setReasonReturn(comment);
        return purchaseOrderMapper.toFullDto(purchaseOrderRepository.save(purchaseOrder), personalShopperRepository);
    }
}
