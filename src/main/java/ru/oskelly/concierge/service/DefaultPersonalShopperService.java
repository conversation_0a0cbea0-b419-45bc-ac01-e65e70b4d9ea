package ru.oskelly.concierge.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperCreateRequest;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;
import ru.oskelly.concierge.data.mapper.PersonalShopperMapper;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;
import ru.oskelly.concierge.data.repository.ShopperCategoryRepository;
import ru.oskelly.concierge.exception.PersonalShopperAlreadyExistsException;
import ru.oskelly.concierge.service.component.PersonalShopperComponent;

@Service
@RequiredArgsConstructor
public class DefaultPersonalShopperService implements PersonalShopperService {
    private final PersonalShopperComponent personalShopperComponent;
    private final PersonalShopperRepository personalShopperRepository;
    private final ShopperCategoryRepository shopperCategoryRepository;
    private final PersonalShopperMapper mapper;

    @Override
    public PersonalShopper createPersonalShopper(PersonalShopperCreateRequest request) {
        if (personalShopperRepository.existsByUserId(request.userId())) {
            throw new PersonalShopperAlreadyExistsException(
                "Personal shopper с user id" + request.userId() + " уже существует.", null, null, 400
            );
        }

        PersonalShopper entity = mapper.toCreateEntity(request, shopperCategoryRepository);

        return personalShopperRepository.save(entity);
    }

    @Override
    public PersonalShopperFilter filterPersonalShopper() {
        return personalShopperComponent.filterPersonalShopper();
    }

    @Override
    public PaginatedShoppersResult getPersonalShoppers(PersonalShopperFilter filter, String searchText) {
        return personalShopperComponent.getPersonalShoppers(filter, searchText);
    }
}
