package ru.oskelly.concierge.service;

import ru.oskelly.concierge.controller.dto.CommentChangeRequest;
import ru.oskelly.concierge.controller.dto.CommentFullDTO;
import ru.oskelly.concierge.controller.dto.CommentListDTO;

/**
 * Сервис работы с комментариями к заявке на покупку
 */
public interface CommentService {
    /**
     * Список комментариев к заявке
     *
     * @param orderId - ID заявки
     * @return - DTO c списком комментариев
     */
    CommentListDTO getComments(Long orderId);

    /**
     * Добавление нового комментария
     *
     * @param orderId ID заявки
     * @param createRequest - данные нового комментария
     * @return - DTO с данными нового комментария
     */
    CommentFullDTO addNewComment(Long orderId, CommentChangeRequest createRequest);

    /**
     * Получение комментария по ИД
     *
     * @param orderId - ИД заявки на покупку
     * @param id - ИД комментария
     * @return - DTO с данными комментария
     */
    CommentFullDTO getComment(Long orderId, Long id);

    /**
     * Удаление комментария по ИД
     *
     * @param orderId - ИД заявки
     * @param id - ИД комментария
     */
    void deleteComment(Long orderId, Long id);

    /**
     * Добавить или убрать закрепление комментария
     *
     * @param orderId - ИД заявки
     * @param id - ИД комментария
     * @return - DTO с данными комментария
     */
    CommentFullDTO pinComment(Long orderId, Long id);

    /**
     * Отредактировать комментарий
     *
     * @param orderId - ИД заявки
     * @param id - ид комментария
     * @param changeRequest - данные для изменения заявки
     * @return {@link CommentFullDTO} с данными комментария
     */
    CommentFullDTO editComment(Long orderId, Long id, CommentChangeRequest changeRequest);
}
