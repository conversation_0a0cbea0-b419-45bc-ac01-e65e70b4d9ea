package ru.oskelly.concierge.config.statemachine;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachineRuntimePersister;
import org.springframework.statemachine.service.DefaultStateMachineService;
import org.springframework.statemachine.service.StateMachineService;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

@Configuration
public class SMServiceConfig {
    @Bean
    public StateMachineService<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachineService(
            StateMachineFactory<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachineFactory,
            StateMachineRuntimePersister<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent, String> stateMachineRuntimePersister) {
        return new DefaultStateMachineService<>(stateMachineFactory, stateMachineRuntimePersister);
    }
}
