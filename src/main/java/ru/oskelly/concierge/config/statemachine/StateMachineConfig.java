package ru.oskelly.concierge.config.statemachine;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.StateMachineConfigurerAdapter;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.guard.Guard;
import org.springframework.statemachine.persist.StateMachineRuntimePersister;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;
import ru.oskelly.concierge.statemachine.action.OrderAssignmentSalesAction;
import ru.oskelly.concierge.statemachine.action.OrderAssignmentSourcerAction;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;
import ru.oskelly.concierge.statemachine.listener.CustomStateMachineListener;

import java.util.EnumSet;

@Slf4j
@Configuration
@EnableStateMachineFactory
@RequiredArgsConstructor
public class StateMachineConfig extends StateMachineConfigurerAdapter<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> {
    private final StateMachineRuntimePersister<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent, String> stateMachineRuntimePersister;
    private final CustomStateMachineListener customStateMachineListener;

    private final OrderAssignmentSalesAction orderAssignmentSalesAction;
    private final OrderAssignmentSourcerAction orderAssignmentSourcerAction;

    @Override
    public void configure(StateMachineStateConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> states)
            throws Exception {
        states.withStates()
                .initial(PurchaseOrderStatusEnum.DRAFT)
                .end(PurchaseOrderStatusEnum.DONE)
                .end(PurchaseOrderStatusEnum.CANCELLED)
                .states(EnumSet.allOf(PurchaseOrderStatusEnum.class));
    }

    @Override
    public void configure(
            StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions)
            throws Exception {
        // 1. Переходы из DRAFT
        // ------------------------------------------------------------
        // Автоматический переход из DRAFT в NEW при выполнении условия isNewGuard()
        addTransitionWithGuard(transitions,
                PurchaseOrderStatusEnum.DRAFT,
                PurchaseOrderStatusEnum.NEW,
                orderAssignmentSalesAction,
                isNewGuard()
        );
        // Автоматический переход из DRAFT в AWAITING_SOURCER при выполнении условия isAwaitingSourcerGuard()
        addTransitionWithGuard(transitions,
                PurchaseOrderStatusEnum.DRAFT,
                PurchaseOrderStatusEnum.AWAITING_SOURCER,
                orderAssignmentSourcerAction,
                isAwaitingSourcerGuard()
        );
        // Ручной переход из DRAFT в NEW по событию SEND_TO_SALES
        addTransition(transitions,
                PurchaseOrderStatusEnum.DRAFT,
                PurchaseOrderTransitionEvent.SEND_TO_SALES,
                PurchaseOrderStatusEnum.NEW,
                orderAssignmentSalesAction
        );

        // 2. Переходы из NEW
        // ------------------------------------------------------------
        // Начало работы отдела продаж: NEW → IN_PROGRESS_SALES
        addTransitionWithGuard(transitions,
                PurchaseOrderStatusEnum.NEW,
                PurchaseOrderTransitionEvent.SALES_WORK_START,
                PurchaseOrderStatusEnum.IN_PROGRESS_SALES,
                isSalesMatched()
        );
        // Отмена заказа отделом продаж: NEW → CANCELLED
        addTransition(transitions,
                PurchaseOrderStatusEnum.NEW,
                PurchaseOrderTransitionEvent.SALES_PURCHASE_CANCELED,
                PurchaseOrderStatusEnum.CANCELLED
        );
        // Отклонение заявки: NEW → REJECTED
        addTransition(transitions,
                PurchaseOrderStatusEnum.NEW,
                PurchaseOrderTransitionEvent.SEND_TO_REJECTION,
                PurchaseOrderStatusEnum.REJECTED
        );
        // Добавление товаров в новую заявку: NEW → AWAITING_SOURCER
        addTransitionWithGuard(transitions,
            PurchaseOrderStatusEnum.NEW,
            PurchaseOrderTransitionEvent.SEND_TO_SOURCER,
            PurchaseOrderStatusEnum.AWAITING_SOURCER,
            orderAssignmentSourcerAction,
            isSalesMatched()
        );

        // 3. Переходы из IN_PROGRESS_SALES
        // ------------------------------------------------------------
        // Передача заказа соурсеру: IN_PROGRESS_SALES → AWAITING_SOURCER
        addTransition(transitions,
                PurchaseOrderStatusEnum.IN_PROGRESS_SALES,
                PurchaseOrderTransitionEvent.SEND_TO_SOURCER,
                PurchaseOrderStatusEnum.AWAITING_SOURCER,
                orderAssignmentSourcerAction
        );
        // Отмена заказа отделом продаж: IN_PROGRESS_SALES → CANCELLED
        addTransition(transitions,
                PurchaseOrderStatusEnum.IN_PROGRESS_SALES,
                PurchaseOrderTransitionEvent.SALES_PURCHASE_CANCELED,
                PurchaseOrderStatusEnum.CANCELLED
        );
        // Отклонение заявки: IN_PROGRESS_SALES → REJECTED
        addTransition(transitions,
                PurchaseOrderStatusEnum.IN_PROGRESS_SALES,
                PurchaseOrderTransitionEvent.SEND_TO_REJECTION,
                PurchaseOrderStatusEnum.REJECTED
        );

        // 4. Переходы из AWAITING_SOURCER
        // ------------------------------------------------------------
        // Начало работы соурсера: AWAITING_SOURCER → IN_PROGRESS_SOURCER
        addTransitionWithGuard(transitions,
                PurchaseOrderStatusEnum.AWAITING_SOURCER,
                PurchaseOrderTransitionEvent.SOURCER_WORK_START,
                PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER,
                isSourcerMatched()
        );
        // Отклонение заявки: AWAITING_SOURCER → REJECTED
        addTransition(transitions,
                PurchaseOrderStatusEnum.AWAITING_SOURCER,
                PurchaseOrderTransitionEvent.SEND_TO_REJECTION,
                PurchaseOrderStatusEnum.REJECTED
        );

        // 5. Переходы из IN_PROGRESS_SOURCER
        // ------------------------------------------------------------
        // Отправка предложения в отдел продаж: IN_PROGRESS_SOURCER → AWAITING_SEND_TO_CLIENT
        addTransition(transitions,
                PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER,
                PurchaseOrderTransitionEvent.SEND_PROPOSAL_TO_SALES,
                PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT
        );
        // Отмена заказа соурсером: IN_PROGRESS_SOURCER → CANCELLED
        addTransition(transitions,
                PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER,
                PurchaseOrderTransitionEvent.SOURCER_PURCHASE_CANCELED,
                PurchaseOrderStatusEnum.CANCELLED
        );
        // Отклонение заявки: IN_PROGRESS_SOURCER → REJECTED
        addTransition(transitions,
                PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER,
                PurchaseOrderTransitionEvent.SEND_TO_REJECTION,
                PurchaseOrderStatusEnum.REJECTED
        );

        // 6. Переходы из AWAITING_SEND_TO_CLIENT
        // ------------------------------------------------------------
        // Отправка предложения клиенту: AWAITING_SEND_TO_CLIENT → AWAITING_CLIENT_ANSWER
        addTransition(transitions,
                PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT,
                PurchaseOrderTransitionEvent.SEND_PURPOSAL_TO_CLIENT,
                PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER
        );
        // Отклонение заявки: AWAITING_SEND_TO_CLIENT → REJECTED
        addTransition(transitions,
                PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT,
                PurchaseOrderTransitionEvent.SEND_TO_REJECTION,
                PurchaseOrderStatusEnum.REJECTED
        );

        // 7. Переходы из AWAITING_CLIENT_ANSWER
        // ------------------------------------------------------------
        // Повторный запрос клиента: AWAITING_CLIENT_ANSWER → REPEAT_REQUEST_TO_SALES
        addTransition(transitions,
                PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER,
                PurchaseOrderTransitionEvent.CLIENT_REPEAT_REQUEST,
                PurchaseOrderStatusEnum.REPEAT_REQUEST_TO_SALES
        );
        // Оплата клиентом: AWAITING_CLIENT_ANSWER → DONE
        addTransition(transitions,
                PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER,
                PurchaseOrderTransitionEvent.PAYED_BY_CUSTOMER,
                PurchaseOrderStatusEnum.DONE
        );

        // 8. Переходы из REPEAT_REQUEST_TO_SALES
        // ------------------------------------------------------------
        // Отправка запроса сорсеру: REPEAT_REQUEST_TO_SALES → REPEAT_AWAITING_SOURCER
        addTransition(transitions,
                PurchaseOrderStatusEnum.REPEAT_REQUEST_TO_SALES,
                PurchaseOrderTransitionEvent.SEND_TO_SOURCER,
                PurchaseOrderStatusEnum.REPEAT_AWAITING_SOURCER
        );

        // 9. Переходы из REPEAT_AWAITING_SOURCER
        // ------------------------------------------------------------
        // Отправка предложения в отдел продаж: REPEAT_AWAITING_SOURCER → AWAITING_SEND_TO_CLIENT
        addTransition(transitions,
                PurchaseOrderStatusEnum.REPEAT_AWAITING_SOURCER,
                PurchaseOrderTransitionEvent.SEND_PROPOSAL_TO_SALES,
                PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT
        );

        // 10. Переходы из PAYED_ORDER_IN_PROGRESS
        // ------------------------------------------------------------
        // Покупатель не нашел товар: PAYED_ORDER_IN_PROGRESS → PAYED_REPEAT_REQUEST
        addTransition(transitions,
                PurchaseOrderStatusEnum.PAYED_ORDER_IN_PROGRESS,
                PurchaseOrderTransitionEvent.BUYER_FAILED,
                PurchaseOrderStatusEnum.PAYED_REPEAT_REQUEST
        );
        // Заказ выполнен: PAYED_ORDER_IN_PROGRESS → DONE
        addTransition(transitions,
                PurchaseOrderStatusEnum.PAYED_ORDER_IN_PROGRESS,
                PurchaseOrderTransitionEvent.ORDER_DONE,
                PurchaseOrderStatusEnum.DONE
        );

        // 11. Переходы из PAYED_REPEAT_REQUEST
        // ------------------------------------------------------------
        // Продолжение запроса: PAYED_REPEAT_REQUEST → PAYEND_RR_SOURCER
        addTransition(transitions,
                PurchaseOrderStatusEnum.PAYED_REPEAT_REQUEST,
                PurchaseOrderTransitionEvent.PROCEED_ORDER,
                PurchaseOrderStatusEnum.PAYED_RR_SOURCER
        );

        // 12. Переходы из PAYEND_RR_SOURCER
        // ------------------------------------------------------------
        // Продолжение обработки заказа: PAYEND_RR_SOURCER → PAYED_ORDER_IN_PROGRESS
        addTransition(transitions,
                PurchaseOrderStatusEnum.PAYED_RR_SOURCER,
                PurchaseOrderTransitionEvent.PROCEED_ORDER,
                PurchaseOrderStatusEnum.PAYED_ORDER_IN_PROGRESS
        );

        // 13. Переходы из REJECTED
        // ------------------------------------------------------------
        // Восстановление после отклонения: REJECTED → IN_PROGRESS_SALES
        addTransition(transitions,
                PurchaseOrderStatusEnum.REJECTED,
                PurchaseOrderTransitionEvent.RESTORE_BY_SALES,
                PurchaseOrderStatusEnum.IN_PROGRESS_SALES
        );
    }

    @Override
    public void configure(
            StateMachineConfigurationConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> config)
            throws Exception {
        config.withPersistence()
                .runtimePersister(stateMachineRuntimePersister);
        config.withConfiguration()
                .listener(customStateMachineListener);
    }

    // Для переходов по событию (событие + цель)
    private void addTransition(
            StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
            PurchaseOrderStatusEnum source,
            PurchaseOrderTransitionEvent event,
            PurchaseOrderStatusEnum target
    ) throws Exception {
        buildTransition(transitions, source, target, event, null, null);
    }

    // Для переходов по событию (событие + цель + действие)
    private void addTransition(
        StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
        PurchaseOrderStatusEnum source,
        PurchaseOrderTransitionEvent event,
        PurchaseOrderStatusEnum target,
        Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> action
    ) throws Exception {
        buildTransition(transitions, source, target, event, action, null);
    }

    // Для переходов с условием (guard) (условие + цель, без события)
    private void addTransitionWithGuard(
            StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
            PurchaseOrderStatusEnum source,
            PurchaseOrderStatusEnum target,
            Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> guard
    ) throws Exception {
        buildTransition(transitions, source, target, null, null, guard);
    }

    // Для переходов с условием (guard) (условие + цель + действие, без события)
    private void addTransitionWithGuard(
        StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
        PurchaseOrderStatusEnum source,
        PurchaseOrderStatusEnum target,
        Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> action,
        Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> guard
    ) throws Exception {
        buildTransition(transitions, source, target, null, action, guard);
    }

    // Для переходов с условием (guard) (условие + цель)
    private void addTransitionWithGuard(
            StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
            PurchaseOrderStatusEnum source,
            PurchaseOrderTransitionEvent event,
            PurchaseOrderStatusEnum target,
            Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> guard
    ) throws Exception {
        buildTransition(transitions, source, target, event, null, guard);
    }

    // Для переходов с условием (guard) (условие + цель + действие)
    private void addTransitionWithGuard(
        StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
        PurchaseOrderStatusEnum source,
        PurchaseOrderTransitionEvent event,
        PurchaseOrderStatusEnum target,
        Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> action,
        Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> guard
    ) throws Exception {
        buildTransition(transitions, source, target, event, action, guard);
    }

    private void buildTransition(
        StateMachineTransitionConfigurer<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> transitions,
        PurchaseOrderStatusEnum source,
        PurchaseOrderStatusEnum target,
        PurchaseOrderTransitionEvent event,
        Action<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> action,
        Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> guard
    ) throws Exception {
        var builder = transitions.withExternal()
            .source(source)
            .target(target);

        if (event != null) {
            builder.event(event);
        }

        if (action != null) {
            builder.action(action);
        }

        if (guard != null) {
            builder.guard(guard);
        }

        builder.and();
    }

    private Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> isNewGuard() {
        return context -> {
            Boolean isNew = (Boolean) context.getExtendedState().getVariables().get("isNew");
            boolean result = isNew != null && isNew;
            log.debug("isNewGuard -> {}", result);
            return result;
        };
    }

    private Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> isSalesMatched() {
        return context ->
                checkForExecutor(context, "salesId", "isSalesMatched");
    }

    private Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> isSourcerMatched() {
        return context ->
                checkForExecutor(context, "sourcerId", "isSourcerMatched");
    }

    /**
     * Проверка соответствия установленного в заявке сотрудника с текущим пользователем
     * @param context - контекст машины
     * @param userKey - ключ id пользователя в контексте машины
     * @param guardKey - ключ кода Guard для логирования
     * @return true - если соответствует иначе false
     * если в контексте не будет userKey возвращается false
     */
    private boolean checkForExecutor(StateContext<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> context,
                                     String userKey, String guardKey) {
        var executorId = (Long) context.getExtendedState().getVariables().get(userKey);
        var userId = (Long) context.getExtendedState().getVariables().get(ContextConstants.USER_ID);
        var role = (Roles) context.getExtendedState().getVariables().get(ContextConstants.ROLE);
        if (role.isAdmin()) {
            return true;
        }
        boolean result = executorId != null && executorId.equals(userId);
        log.debug("{} -> {}", guardKey, result);

        // Для ошибки
        if (!result) {
            ThreadLocalContext.put(ContextConstants.STATE_MACHINE_ERROR, true);
            ThreadLocalContext.put(ContextConstants.STATE_MACHINE_ERROR_MESSAGE,
                    String.format("Пользователь с ID %s не соответствует установленному в заявке %s",
                            userId, executorId));
        }
        return result;
    }

    private Guard<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> isAwaitingSourcerGuard() {
        return context -> {
            Boolean isAwaitingSourcer = (Boolean) context.getExtendedState().getVariables().get("isAwaitingSourcer");
            boolean result = isAwaitingSourcer != null && isAwaitingSourcer;
            log.debug("isAwaitingSourcerGuard -> {}", result);
            return result;
        };
    }
}
