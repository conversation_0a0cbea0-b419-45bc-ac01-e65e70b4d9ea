package ru.oskelly.concierge.config.statemachine;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.data.jpa.JpaPersistingStateMachineInterceptor;
import org.springframework.statemachine.data.jpa.JpaStateMachineRepository;
import org.springframework.statemachine.persist.StateMachineRuntimePersister;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;

@Configuration
public class JpaSMPersisterConfig {
    @Bean
    public StateMachineRuntimePersister<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent, String> stateMachineRuntimePersister
            (
                    JpaStateMachineRepository jpaStateMachineRepository
            ) {
        return new JpaPersistingStateMachineInterceptor<>(jpaStateMachineRepository);
    }
}
