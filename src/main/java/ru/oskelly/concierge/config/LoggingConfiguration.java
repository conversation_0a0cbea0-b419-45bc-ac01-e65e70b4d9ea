package ru.oskelly.concierge.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.zalando.logbook.Logbook;
import org.zalando.logbook.core.DefaultCorrelationId;
import org.zalando.logbook.core.DefaultSink;
import org.zalando.logbook.core.RequestFilters;
import org.zalando.logbook.core.ResponseFilters;
import org.zalando.logbook.core.StreamHttpLogWriter;
import ru.oskelly.concierge.common.CustomHttpLogFormatter;

import static org.zalando.logbook.core.Conditions.contentType;
import static org.zalando.logbook.core.Conditions.exclude;
import static org.zalando.logbook.core.Conditions.requestTo;
import static org.zalando.logbook.core.Conditions.requestWithMethod;
import static org.zalando.logbook.core.HeaderFilters.authorization;
import static org.zalando.logbook.core.QueryFilters.accessToken;

@Configuration
public class LoggingConfiguration {

    @Bean
    @Primary
    public Logbook customLogbook() {
        return Logbook.builder()
                .correlationId(new DefaultCorrelationId())
                .condition(
                        exclude(requestTo("/prometheus")
                                .and(requestWithMethod("GET")))
                )
                .condition(
                        exclude(requestTo("/actuator/health")
                                .and(requestWithMethod("GET")))
                )
                .sink(new DefaultSink(
                        new CustomHttpLogFormatter(new ObjectMapper()),
                        new StreamHttpLogWriter()
                ))
                .requestFilter(RequestFilters.replaceBody(request ->
                        contentType("multipart/form-data").test(request)
                                ? "Содержимое файла не логируется"
                                : null
                ))
                .responseFilter(ResponseFilters.replaceBody(response ->
                        contentType("*/*-stream").test(response)
                                ? "Поток с файлом не логируется"
                                : null
                ))
                .queryFilter(accessToken())
                .headerFilter(authorization())
                .build();
    }
}
