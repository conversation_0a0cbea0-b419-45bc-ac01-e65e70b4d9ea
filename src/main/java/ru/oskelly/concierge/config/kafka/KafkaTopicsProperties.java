package ru.oskelly.concierge.config.kafka;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Getter
@Component
@ConfigurationProperties(prefix = "kafka")
public class KafkaTopicsProperties {
    private final Map<String, TopicConfig> topics = new HashMap<>();

    public boolean isTopicEnabled(String topicName) {
        return topics.entrySet()
            .stream()
            .filter(entry -> entry.getValue().name.equals(topicName))
            .findFirst()
            .map(Map.Entry::getValue)
            .map(TopicConfig::isEnabled)
            .orElseThrow(() -> new IllegalArgumentException("Topic " + topicName + " not found"));
    }

    @Getter
    @AllArgsConstructor
    public static class TopicConfig {
        private String name;
        private boolean enabled;
    }
}
