package ru.oskelly.concierge.config.kafka;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListenerConfigurer;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerEndpointRegistrar;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DeadLetterPublishingRecoverer;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.kafka.support.converter.StringJsonMessageConverter;
import org.springframework.kafka.support.serializer.DeserializationException;
import org.springframework.util.backoff.FixedBackOff;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.util.Map;

@Slf4j
@EnableKafka
@Configuration
@RequiredArgsConstructor
public class KafkaConfig implements KafkaListenerConfigurer {

    private final LocalValidatorFactoryBean validator;

    /**
     * Настройка фабрики контейнеров для слушателей Kafka.
     * По-умолчанию применятся ко всем слушателям. Использует StringJsonMessageConverter
     * для преобразования JSON в указанные объекты слушателей.
     */
    @Bean
    public KafkaListenerContainerFactory<?> kafkaListenerContainerFactory(
        ConsumerFactory<Integer, String> consumerFactory,
        DefaultErrorHandler errorHandler
    ) {
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory =
            new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(consumerFactory);

        // Десериализация сообщений
        factory.setRecordMessageConverter(new StringJsonMessageConverter());

        // Настройка подтверждения обработки потребителем
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        // Настройка обработки ошибок
        factory.setCommonErrorHandler(errorHandler);

        return factory;
    }

    /**
     * KafkaTemplate для отправки сообщений в Kafka
     */
    @Bean
    @Primary
    public KafkaTemplate<String, Object> kafkaTemplate(ProducerFactory<String, Object> producerFactory) {
        return new KafkaTemplate<>(producerFactory);
    }

    /**
     * KafkaTemplate для отправки сообщений в Dead Letter Topics Kafka
     */
    @Bean
    public KafkaTemplate<String, Object> dltKafkaTemplate(KafkaProperties kafkaProperties) {
        Map<String, Object> props = kafkaProperties.buildProducerProperties();
        // Переопределение сериализатора для отправки сообщений в DLT
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(props));
    }

    /**
     * Настройка обработки ошибок и отправки неудавшихся сообщений в DLT (Dead Letter Topic)
     */
    @Bean
    public DefaultErrorHandler errorHandler(@Qualifier("dltKafkaTemplate") KafkaTemplate<String, Object> kafkaTemplate) {
        DeadLetterPublishingRecoverer recoverer = new DeadLetterPublishingRecoverer(
            kafkaTemplate,
            (record, ex) -> {
                log.error(
                    "Ошибка при обработке сообщения из топика Kafka [{}], offset={}, partition={}, key={}, value={}",
                    record.topic(), record.offset(), record.partition(), record.key(), record.value(), ex
                );

                String originalTopic = record.topic();
                String dltTopic = originalTopic + ".dlt";
                return new TopicPartition(dltTopic, record.partition());
            }
        );

        // Количество попыток обработки сообщения потребителем
        DefaultErrorHandler errorHandler = new DefaultErrorHandler(
            recoverer,
            new FixedBackOff(1000L, 3L)
        );

        errorHandler.addNotRetryableExceptions(DeserializationException.class);

        return errorHandler;
    }

    /**
     * Включение валидации для потребителей
     */
    @Override
    public void configureKafkaListeners(KafkaListenerEndpointRegistrar registrar) {
        registrar.setValidator(validator);
    }
}
