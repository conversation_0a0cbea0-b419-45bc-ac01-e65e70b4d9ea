package ru.oskelly.concierge.kafka.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.data.model.enums.Roles;

import java.util.List;

/**
 * Результат отправки сообщений с предложениями в WhatsApp клиенту
 * @param success результат
 * @param error текст ошибки
 * @param orderId идентификатор заявки
 * @param userId идентификатор пользователя, выполняющего отправку
 * @param text текст отправленного сообщения
 */
public record ClientOfferWhatsAppSendResultMessage(
    @NotNull(message = "Результат операции не может быть пустым")
    Boolean success,

    String error,

    @NotNull(message = "ID заявки не может быть пустым")
    Long orderId,

    @NotNull(message = "ID пользователя, отправившего сообщение, не может быть пустым")
    Long userId,

    List<Long> offerIds,

    List<Long> proposedOfferIds,

    @NotNull(message = "Роль пользователя, отправившего сообщение, не может быть пустым")
    Roles role,

    @NotBlank(message = "Отправленное сообщение не может быть пустым")
    String text
) {
}
