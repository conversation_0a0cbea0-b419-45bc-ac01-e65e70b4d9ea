package ru.oskelly.concierge.kafka.dto;

import java.util.List;

/**
 * Сообщение-запрос на отправку предложений клиенту в WhatsApp
 * @param salesId идентификатор Сейлза
 * @param userId идентификатор пользователя, отправляющего сообщение
 * @param orderId идентификатор заявки
 * @param offerIds список предложений для отметки об отправке после подтверждения
 * @param proposedOfferIds список предложений байера для отметки об отправке после подтверждения
 * @param phone телефон клиента
 * @param text текст сообщения с предложениями
 */
public record SendClientOfferWhatsAppMessage(
    Long salesId,
    Long userId,
    Long orderId,
    List<Long> offerIds,
    List<Long> proposedOfferIds,
    String phone,
    String text
) {
}
