package ru.oskelly.concierge.kafka.dto;

import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * Запрос на перераспределение заявок Сейлза
 * @param sourceSalesId идентификатор Сейлза, держащего заявки
 * @param targetSales данные Сейлза для переназначения
 */
public record SalesOrderReassignmentRequestMessage(
    Long sourceSalesId,
    TargetSales targetSales
) {
    public record TargetSales(
        Long salesId,
        String fio,
        Roles role
    ) {
    }
}
