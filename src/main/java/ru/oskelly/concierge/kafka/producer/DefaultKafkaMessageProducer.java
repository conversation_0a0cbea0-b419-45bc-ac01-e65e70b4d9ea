package ru.oskelly.concierge.kafka.producer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.config.kafka.KafkaTopicsProperties;
import ru.oskelly.concierge.exception.KafkaProducerException;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultKafkaMessageProducer implements KafkaMessageProducer {
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final KafkaTopicsProperties topicsProperties;

    @Override
    public void sendMessage(String topic, Object message) {
        try {
            if (!topicsProperties.isTopicEnabled(topic)) {
                log.warn("Топик {} отключен. Сообщение не отправлено.", topic);
                return;
            }

            send(topic, message);
        } catch (Exception e) {
            String errorMessage = "Возникла ошибка при отправке сообщения: "  +  e.getMessage();
            log.error(errorMessage);
            throw new KafkaProducerException(errorMessage, e, null, 503);
        }
    }

    @Override
    public void sendBatchMessages(String topic, List<?> messages) {
        try {
            if (!topicsProperties.isTopicEnabled(topic)) {
                log.warn("Топик {} отключен. Сообщения не отправлены.", topic);
                return;
            }

            messages.forEach(message -> send(topic, message));
            kafkaTemplate.flush();
        } catch (Exception e) {
            String errorMessage = "Возникла ошибка при отправке партии сообщений: " + e.getMessage();
            log.error(errorMessage);
            throw new KafkaProducerException(errorMessage, e, null, 503);
        }
    }

    /**
     * Отправка сообщения в Kafka и обработка результата
     * @param topic наименование топика
     * @param message сообщение
     */
    private void send(String topic, Object message) {
        CompletableFuture<SendResult<String, Object>> future = kafkaTemplate.send(topic, message);

        future.whenComplete((result, ex) -> {
            if (Objects.isNull(ex)) {
                log.info("Запрос '{}' успешно отправлен. Топик - {}", result.toString(), topic);
            } else {
                log.error("Не удалось отправить запрос: '{}'. Ошибка: '{}'. Топик - {}", result.toString(), ex, topic);
            }
        });
    }
}
