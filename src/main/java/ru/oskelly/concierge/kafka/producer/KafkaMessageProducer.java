package ru.oskelly.concierge.kafka.producer;

import java.util.List;

/**
 * Компонент отправки сообщений в топики Kafka
 */
public interface KafkaMessageProducer {

    /**
     * Отправка сообщения в топик
     * @param topic наименование топика
     * @param message сообщение
     */
    void sendMessage(String topic, Object message);

    /**
     * Отправка партии сообщений в топик
     * @param topic наименование топика
     * @param messages список сообщений
     */
    void sendBatchMessages(String topic, List<?> messages);
}
