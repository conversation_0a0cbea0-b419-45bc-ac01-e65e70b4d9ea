package ru.oskelly.concierge.kafka.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.kafka.dto.SalesOrderReassignmentRequestMessage;
import ru.oskelly.concierge.service.OrderAssignmentSalesService;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class SalesOrderReassignmentRequestListener {
    private final OrderAssignmentSalesService orderAssignmentSalesService;

    @KafkaListener(
        topics = "${kafka.topics.sales-requests-reassignment-orders-topic.name}",
        autoStartup = "${kafka.topics.sales-requests-reassignment-orders-topic.enabled}"
    )
    public void handleEvent(@Payload SalesOrderReassignmentRequestMessage message, Acknowledgment ack) {
        SalesInfoDTO targetSalesDto = Optional.ofNullable(message.targetSales())
            .map(targetSales -> new SalesInfoDTO(
                targetSales.salesId(),
                targetSales.fio(),
                null,
                null,
                targetSales.role()
            ))
            .orElse(null);

        orderAssignmentSalesService.redistribute(message.sourceSalesId(), targetSalesDto);

        ack.acknowledge();
    }
}
