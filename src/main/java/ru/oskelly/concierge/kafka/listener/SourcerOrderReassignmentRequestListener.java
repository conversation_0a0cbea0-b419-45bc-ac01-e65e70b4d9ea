package ru.oskelly.concierge.kafka.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.kafka.dto.SourcerOrderReassignmentRequestMessage;
import ru.oskelly.concierge.service.OrderAssignmentSourcerService;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class SourcerOrderReassignmentRequestListener {
    private final OrderAssignmentSourcerService orderAssignmentSourcerService;

    @KafkaListener(
        topics = "${kafka.topics.sourcer-requests-reassignment-orders-topic.name}",
        autoStartup = "${kafka.topics.sourcer-requests-reassignment-orders-topic.enabled}"
    )
    public void handleEvent(@Payload SourcerOrderReassignmentRequestMessage message, Acknowledgment ack) {
        SourcerInfoDTO targetSourcerDto = Optional.ofNullable(message.targetSourcer())
            .map(targetSourcer -> SourcerInfoDTO.builder()
                    .sourcerId(targetSourcer.sourcerId())
                    .build()
                    )
            .orElse(null);

        orderAssignmentSourcerService.redistribute(message.sourceSourcerId(), targetSourcerDto);

        ack.acknowledge();
    }
}
