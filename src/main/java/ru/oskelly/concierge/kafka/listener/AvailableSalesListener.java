package ru.oskelly.concierge.kafka.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.kafka.dto.AvailableSalesMessage;
import ru.oskelly.concierge.service.OrderAssignmentSalesService;

@Component
@RequiredArgsConstructor
public class AvailableSalesListener {

    private final OrderAssignmentSalesService orderAssignmentSalesService;

    @KafkaListener(
        topics = "${kafka.topics.sales-available-topic.name}",
        autoStartup = "${kafka.topics.sales-available-topic.enabled}"
    )
    public void handleEvent(@Payload AvailableSalesMessage message, Acknowledgment ack) {
        SalesInfoDTO sales = new SalesInfoDTO(
            message.salesId(),
            message.fio(),
            null,
            null,
            message.role()
        );

        orderAssignmentSalesService.assignToSales(message.orderId(), sales);

        ack.acknowledge();
    }
}
