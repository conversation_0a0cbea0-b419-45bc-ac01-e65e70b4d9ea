package ru.oskelly.concierge.statemachine.events;

import lombok.Getter;

/**
 * Перечисление событий перехода состояний заказа на покупку
 */
@Getter
public enum PurchaseOrderTransitionEvent {
    SEND_TO_SALES("Переход в статус NEW"),
    SALES_WORK_START("Начало работы Sales менеджер"),
    SEND_TO_SOURCER("Отправка заявки Сорсер менеджеру"),
    SOURCER_WORK_START("Начало работы Сорсер менеджера"),
    SEND_PROPOSAL_TO_SALES("Отправка предложения Sales менеджеру"),
    SEND_PURPOSAL_TO_CLIENT("Отправка предложения клиенту"),
    CLIENT_REPEAT_REQUEST("Повторный запрос от клиента"),
    PAYED_BY_CUSTOMER("Оплачено клиентом"),
    BUYER_FAILED("Покупатель не нашел товар"),
    PROCEED_ORDER("Продолжить обработку заказа"),
    ORDER_DONE("Заказ выполнен"),
    SALES_PURCHASE_CANCELED("Отмена заявки Sales менеджером"),
    SOURCER_PURCHASE_CANCELED("Отмена заявки Сорсер-менеджером"),
    SEND_TO_REJECTION("Отклонение заявки"),
    RESTORE_BY_SALES("Восстановление после отклонения");

    private final String description;
    PurchaseOrderTransitionEvent(String description) {
        this.description = description;
    }
}
