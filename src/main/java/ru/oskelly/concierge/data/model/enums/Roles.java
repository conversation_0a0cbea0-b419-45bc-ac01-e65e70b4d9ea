package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * Роли пользователей в системе
 */
@Getter
@Schema(
        description = "Перечисление ролей пользователей в системе",
        enumAsRef = true
)
@AllArgsConstructor
public enum Roles {
    @Schema(
            description = "Менеджер по продажам",
            example = "SALES"
    )
    SALES("Менеджер по продажам"),

    @Schema(
            description = "Менеджер, ответственный за подбор предложений для заказа",
            example = "SOURCER"
    )
    SOURCER("Менеджер, ответственный за подбор предложений для заказа."),

    @Schema(
            description = "Покупатель/клиент системы",
            example = "CUSTOMER"
    )
    CUSTOMER("Покупатель"),

    @Schema(
            description = "Продавец бутика Столешников",
            example = "STOLESHNIKOV_BOUTIQUE_SALESMAN"
    )
    STOLESHNIKOV_BOUTIQUE_SALESMAN("Продавец бутика Столешников"),

    @Schema(
            description = "Продавец бутика Столешников",
            example = "KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN"
    )
    KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN("Продавец бутика Кузнецкий Мост"),

    @Schema(
            description = "Администратор бутика Столешников",
            example = "STOLESHNIKOV_ADMIN"
    )
    STOLESHNIKOV_ADMIN("Администратор Столешников"),

    @Schema(
            description = "Администратор бутика Кузнецкий Мост",
            example = "KUZNETSKY_BRIDGE_ADMIN"
    )
    KUZNETSKY_BRIDGE_ADMIN("Администратор Кузнецкий Мост"),

    @Schema(
            description = "Администратор Консьерж Сейлз",
            example = "CONCIERGE_SALES_ADMIN"
    )
    CONCIERGE_SALES_ADMIN("Администратор Консьерж Сейлз"),

    @Schema(
            description = "Администратор Консьерж Сорсерсы",
            example = "CONCIERGE_SOURCERS_ADMIN"
    )
    CONCIERGE_SOURCERS_ADMIN("Администратор Консьерж Сорсерсы"),

    @Schema(
        description = "Супер-администратор Mercaux",
        example = "MERCAUX_ADMIN"
    )
    MERCAUX_ADMIN("Супер-администратор Mercaux"),

    @Schema(
            description = "Система",
            example = "SYSTEM"
    )
    SYSTEM("Система");

    @Schema(
            description = "Человеко-читаемое название роли",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private final String name;

    public boolean isAdmin() {
        Set<Roles> adminRoles = Set.of(CONCIERGE_SALES_ADMIN, CONCIERGE_SOURCERS_ADMIN, STOLESHNIKOV_ADMIN, KUZNETSKY_BRIDGE_ADMIN, SYSTEM);
        return adminRoles.contains(this);
    }

}
