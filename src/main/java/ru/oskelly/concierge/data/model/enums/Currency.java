package ru.oskelly.concierge.data.model.enums;

import lombok.Getter;

@Getter
public enum Currency {

    USD("Доллары"),
    AED("Дирхамы"),
    EUR("Евро"),
    KRW("Корейские воны"),
    KGS("Кыргызские сомы"),
    RUB("Российские рубли"),
    TL("Турецкие лиры"),
    CNY("Китайские юани"),
    JPY("Японские йены");

    private final String description;

    Currency(String description) {
        this.description = description;
    }

}
