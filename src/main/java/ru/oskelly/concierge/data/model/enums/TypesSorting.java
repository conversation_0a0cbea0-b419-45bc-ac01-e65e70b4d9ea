package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * Перечисление, представляющее доступные типы сортировки для запросов
 */
@Getter
@Schema(
        description = "Определяет доступные типы сортировки для наборов результатов",
        enumAsRef = true,
        defaultValue = "DATE"
)
public enum TypesSorting {

    @Schema(
            description = "Сортировать результаты по статусу")
    STATUS("Сортировка по статусу", false),

    @Schema(
            description = "Сортировать результаты по дате (сортировка по умолчанию)")
    DATE("Сортировка по дате", true);

    @Schema(
            description = "Человеко-читаемое описание типа сортировки",
            example = "Сортировка по дате",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private final String description;

    @Schema(
            description = "Флаг, указывающий, является ли этот тип сортировки используемым по умолчанию",
            example = "true",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private final boolean isDefault;

    TypesSorting(String description, boolean isDefault) {
        this.description = description;
        this.isDefault = isDefault;
    }

    public boolean isDate() {
        return this == DATE;
    }

    public boolean isStatus() {
        return this == STATUS;
    }
}
