package ru.oskelly.concierge.data.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Index;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import ru.oskelly.concierge.data.model.enums.Currency;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 *
 */
@Getter
@Setter
@Entity
@Builder
@Table(name = "proposed_offer", indexes = {
    @Index(name = "idx_proposed_offer_product_id", 
           columnList = "proposed_product_id"),
    @Index(name = "idx_proposed_offer_offer_id", 
           columnList = "offer_id"),
    @Index(name = "idx_proposed_offer_order_id", 
           columnList = "order_id")
})
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class ProposedOffer {
    /**
     * Уникальный идентификатор.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "proposed_offer_generator")
    @SequenceGenerator(name = "proposed_offer_generator", sequenceName = "  proposed_offer_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * Идентификатор товара, созданного на основе предложения на платформе.
     */
    @Column(name = "proposed_product_id")
    private Long proposedProductId;

    /**
     * Цена в рублях
     */
    @Column(name = "ruble_price")
    private BigDecimal rublePrice;

    /**
     * Дата доставки по предложению.
     */
    @Column(name = "delivery_date")
    private ZonedDateTime deliveryDate;

    /**
     * Срок действия предложения.
     */
    @Column(name = "valid_until")
    private ZonedDateTime validUntil;


    @CreatedDate
    @Column(name = "creation_date")
    private ZonedDateTime creationDate;

    /**
     * Валюта закупки
     */
    @Column(name = "currency")
    @Enumerated(EnumType.STRING)
    private Currency currency;

    /**
     * Цена в валюте
     */
    @Column(name = "currency_price")
    private BigDecimal currencyPrice;

    /**
     * Курс валюты к рублю
     * */
    @Column(name = "currency_rate")
    private BigDecimal currencyRate;

    /**
     * Флаг наличия чека
     */
    @Column(name = "has_receipt")
    private boolean hasReceipt;

    /**
     * Флаг полного комплекта
     */
    @Column(name = "is_complete_set")
    private boolean isCompleteSet;

    /**
     * Флаг произвольной комиссии
     */
    @Column(name = "has_custom_commission")
    private boolean hasCustomCommission;

    /**
     * Размер комиссии
     */
    @Column(name = "commission")
    private BigDecimal commission;

    /**
     * Комментарий к предложению
     */
    @Column(name = "comment")
    private String comment;

    /**
     * Идентификатор состояния товара
     */
    @Column(name = "product_condition_id")
    private Long productConditionId;

    /**
     * Идентификатор заказа, к которому относится предложение.
     */
    @Column(name = "order_id")
    private Long orderId;

    @Builder.Default
    @Column(name = "is_sent_to_customer")
    private Boolean isSentToCustomer = Boolean.FALSE;


    @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "offer_id")
    private Offer offer;
}
