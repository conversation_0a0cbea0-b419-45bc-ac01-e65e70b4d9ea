package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(
        description = "Перечисление типов предложений товаров/услуг",
        enumAsRef = true
)
@Getter
public enum OfferType {
    @Schema(
            description = "Товар с платформы (внутренний товар)",
            example = "PLATFORM_PRODUCT"
    )
    PLATFORM_PRODUCT("Товар с платформы"),

    @Schema(
            description = "Предложение от покупателя (внешнее предложение)",
            example = "BUYER_OFFER"
    )
    BUYER_OFFER("Предложение покупателя");


    @Schema(
            description = "Человеко-читаемое описание типа предложения",
            example = "Товар с платформы",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private final String description;

    OfferType(String description) {
        this.description = description;
    }
}
