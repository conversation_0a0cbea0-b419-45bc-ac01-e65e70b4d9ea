package ru.oskelly.concierge.data.model.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

@Converter
public class PurchaseOrderSourceConverter implements AttributeConverter<PurchaseOrderSourceEnum, String> {

    @Override
    public String convertToDatabaseColumn(PurchaseOrderSourceEnum attribute) {
        return attribute != null ? attribute.getDescription() : null;
    }

    @Override
    public PurchaseOrderSourceEnum convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }

        for (PurchaseOrderSourceEnum source : PurchaseOrderSourceEnum.values()) {
            if (source.getDescription().equals(dbData)) {
                return source;
            }
        }

        throw new IllegalArgumentException("Unknown database value: " + dbData);
    }
}
