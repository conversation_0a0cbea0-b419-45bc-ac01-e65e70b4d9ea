package ru.oskelly.concierge.data.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.time.ZonedDateTime;

@Entity
@Table(name = "purchase_order_state_history")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PurchaseOrderStateHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "purchase_order_state_history_generator")
    @SequenceGenerator(name = "purchase_order_state_history_generator", sequenceName = "purchase_order_state_history_seq", allocationSize = 1)
    private Long id;

    @Column(name = "purchase_order_id", nullable = false)
    private Long purchaseOrderId;

    @Column(name = "user_id")
    private Long userId;

    @Enumerated(EnumType.STRING)
    @Column(name = "source_state")
    private PurchaseOrderStatusEnum sourceState;

    @Enumerated(EnumType.STRING)
    @Column(name = "target_state", nullable = false)
    private PurchaseOrderStatusEnum targetState;

    @Column(name = "transition_date", nullable = false)
    private ZonedDateTime transitionDate;

    @Column(name = "comment")
    private String comment;

    @Column(name = "reason_return")
    private String reasonReturn;
}
