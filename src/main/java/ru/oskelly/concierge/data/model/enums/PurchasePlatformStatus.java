package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(
        description = "Перечисление статусов предложений товаров",
        enumAsRef = true
)
@Getter
public enum PurchasePlatformStatus {
    @Schema(
            description = "Товар доступен для покупки",
            example = "AVAILABLE"
    )
    AVAILABLE("доступен"),

    @Schema(
            description = "Товар упущен, больше недоступен",
            example = "MISSED"
    )
    MISSED("упущен");

    @Schema(
            description = "Человеко-читаемое описание статуса предложения",
            example = "доступен",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    private final String description;

    PurchasePlatformStatus(String description) {
        this.description = description;
    }
}