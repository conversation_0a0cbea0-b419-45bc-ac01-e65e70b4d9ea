package ru.oskelly.concierge.data.model.enums;

import lombok.Getter;

@Getter
public enum PaymentFormat {
    PREPAYMENT("Предоплата"),
    POSTPAYMENT("Постоплата");

    private final String description;

    PaymentFormat(String description) {
        this.description = description;
    }

    public static PaymentFormat fromDescription(String description) {
        for (PaymentFormat format : values()) {
            if (format.getDescription().equalsIgnoreCase(description)) {
                return format;
            }
        }
        throw new IllegalArgumentException("Unknown description: " + description);
    }
}
