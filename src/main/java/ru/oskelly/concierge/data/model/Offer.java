package ru.oskelly.concierge.data.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Index;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import ru.oskelly.concierge.data.model.enums.PurchasePlatformStatus;
import ru.oskelly.concierge.data.model.enums.OfferType;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Сущность, представляющая предложение баера для заказа.
 * <p>
 * Данная сущность содержит информацию о предложении, сделанном продавцом
 * для конкретной отправки, включая цену, тип предложения, сроки доставки
 * и период действия предложения.
 */
@Getter
@Setter
@Entity
@Builder
@Table(name = "offer", indexes = {
    @Index(name = "idx_offer_product_id", 
           columnList = "product_id"),
    @Index(name = "idx_offer_sent_to_customer", 
           columnList = "is_sent_to_customer"),
    @Index(name = "idx_offer_bitrix_order", 
           columnList = "bitrix_id, order_id"),
    @Index(name = "idx_offer_platform_status", 
           columnList = "purchase_platform_status"),
    @Index(name = "idx_offer_shipment_id", 
           columnList = "shipment_id")
})
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Offer {

    /**
     * Уникальный идентификатор предложения.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "offer_generator")
    @SequenceGenerator(name = "offer_generator", sequenceName = "offer_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * Отправка, к которой относится предложение.
     */
    @ManyToOne(optional = false)
    @JoinColumn(name = "shipment_id", nullable = false)
    private Shipment shipment;

    /**
     * Идентификатор продавца, сделавшего предложение.
     */
    @Column(name = "seller_id")
    private Long sellerId;

    /**
     * Идентификатор товара, существующего на платформе.
     */
    @Column(name = "product_id")
    private Long productId;

    /**
     * Тип предложения.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private OfferType type;

    /**
     * Статус товара с платформы в предложении.
     */
    @Enumerated(EnumType.STRING)
    @Builder.Default
    @Column(name = "purchase_platform_status")
    private PurchasePlatformStatus purchasePlatformStatus = PurchasePlatformStatus.AVAILABLE;

    /**
     * Тип продавца, сделавшего предложение.
     */
    @Column(name = "seller_type")
    private String sellerType;

    /**
     * Текущий пользователь, который создал предложение.
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * Идентификатор заказа, к которому относится предложение.
     */
    @Column(name = "order_id")
    private Long orderId;

    @Builder.Default
    @Column(name = "is_sent_to_customer")
    private Boolean isSentToCustomer = Boolean.FALSE;

    @CreatedDate
    @Column(name = "creation_date")
    private ZonedDateTime creationDate;

    @OneToMany(mappedBy = "offer", cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @Builder.Default
    private Set<ProposedOffer> proposedOffers = new HashSet<>();

    @Column(name = "bitrix_id")
    private Long bitrixId;
}