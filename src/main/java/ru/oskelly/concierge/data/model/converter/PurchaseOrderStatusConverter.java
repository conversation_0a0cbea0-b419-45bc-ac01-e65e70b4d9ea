package ru.oskelly.concierge.data.model.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

@Converter
public class PurchaseOrderStatusConverter implements AttributeConverter<PurchaseOrderStatusEnum, String> {

    @Override
    public String convertToDatabaseColumn(PurchaseOrderStatusEnum attribute) {
        return attribute != null ? attribute.getDescription() : null;
    }

    @Override
    public PurchaseOrderStatusEnum convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }

        for (PurchaseOrderStatusEnum status : PurchaseOrderStatusEnum.values()) {
            if (status.getDescription().equals(dbData)) {
                return status;
            }
        }

        throw new IllegalArgumentException("Unknown database value: " + dbData);
    }
}
