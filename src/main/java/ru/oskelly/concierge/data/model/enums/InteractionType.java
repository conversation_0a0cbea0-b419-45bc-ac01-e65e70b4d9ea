package ru.oskelly.concierge.data.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * Тип взаимодействия с системой
 */
@Getter
@Schema(description = "Тип взаимодействия с системой")
public enum InteractionType {

    @Schema(description = "Интернет сайты")
    INTERNET_SITES("Интернет сайты"),

    @Schema(description = "Мультибренд")
    MULTIBRAND("Мультибренд"),

    @Schema(description = "Под запрос")
    ON_REQUEST("Под запрос"),

    @Schema(description = "Универсал")
    UNIVERSAL("Универсал");

    @Schema(description = "Описание типа взаимодействия")
    private final String value;

    @JsonCreator
    InteractionType(String value) {
        this.value = value;
    }

//    @JsonValue
    public String getValue() {
        return value;
    }

    public static InteractionType fromDescription(String description) {
        for (InteractionType type : values()) {
            if (type.getValue().equalsIgnoreCase(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown description: " + description);
    }
}