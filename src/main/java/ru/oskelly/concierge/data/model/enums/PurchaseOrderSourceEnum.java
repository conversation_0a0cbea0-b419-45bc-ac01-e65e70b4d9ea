package ru.oskelly.concierge.data.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * Перечисление источников создания заявки
 */
@Schema(description = "Источники создания заявки на покупку")
@Getter
public enum PurchaseOrderSourceEnum {
    @Schema(description = "Заявка создана через мобильное приложение")
    APP("Приложение"),

    @Schema(description = "Заявка создана через веб-сайт")
    WEB("Web"),

    @Schema(description = "Заявка создана через Telegram")
    TELEGRAM("Telegram"),

    @Schema(description = "Заявка создана через WhatsApp")
    WHATSAPP("Whatsapp"),

    @Schema(description = "Заявка создана через Instagram")
    INSTAGRAM("Instagram"),

    @Schema(description = "Заявка создана из комментариев")
    COMMENTS("Комментарии"),

    @Schema(description = "Заявка создана через витрину")
    SHOWCASE("Витрина"),

    @Schema(description = "Заявка создана через Concierge")
    CONCIERGE("Concierge"),

    @Schema(description = "Заявка создана через Cancelled Orders")
    CANCELLED_ORDERS("Cancelled Orders"),

    @Schema(description = "Заявка создана через Personal Shopping")
    PERSONAL_SHOPPING("Personal Shopping"),

    @Schema(description = "Заявка создана через Product Page")
    PRODUCT_PAGE("Product Page"),

    @Schema(description = "Заявка создана через WishList")
    WISHLIST("WishList"),

    @Schema(description = "Заявка создана через Cart")
    CART("Cart"),

    @Schema(description = "Заявка создана через O!Trends")
    O_TRENDS("O!Trends"),

    @Schema(description = "Заявка создана через Sales Admin")
    SALES_ADMIN("Sales: Admin"),

    @Schema(description = "Заявка создана через Sales App")
    SALES_APP("Sales: App"),

    @Schema(description = "Заявка создана через чат поддержки")
    CS_SUPPORT_CHAT("CS: Чат поддержки"),

    @Schema(description = "Заявка создана через Affiliate партнера")
    PARTNERS_AFFILIATE("Partners: Affiliate");

    @Schema(description = "Человеко-читаемое описание источника")
    private final String description;

    @JsonValue
    public String toValue() {
        return this.name(); // Будет возвращать "APP", "WEB" и т.д.
    }

    @JsonCreator
    public static PurchaseOrderSourceEnum fromValue(String value) {
        try {
            return PurchaseOrderSourceEnum.valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Unknown enum value: " + value);
        }
    }

    PurchaseOrderSourceEnum(String description) {
        this.description = description;
    }
}