package ru.oskelly.concierge.data.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import ru.oskelly.concierge.data.model.enums.ObjectType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * Справочник причин отклонения объектов
 * Поля orderStatus и objectType используются только для отображения на фронте и никак не контролируются
 */
@Entity
@Table(name = "rejection_reason")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RejectionReason {

    /**
     * Уникальный идентификатор
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "rejection_reason_seq")
    @SequenceGenerator(name = "rejection_reason_seq", sequenceName = "rejection_reason_id_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * Тип объекта (заявка или товар)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "object_type", nullable = false)
    private ObjectType objectType;

    /**
     * Статус заявки (только для типа объекта ORDER)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "order_status")
    private PurchaseOrderStatusEnum orderStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "role")
    private Roles role;

    /**
     * Текст причины отклонения
     */
    @Column(name = "reason_text", nullable = false)
    private String reasonText;

    /**
     * Флаг необходимости указания дополнительного описания
     */
    @Column(name = "requires_description", nullable = false)
    private boolean requiresDescription;


    /**
     * Валидация перед сохранением: статус разрешен только для заявок
     */
    @PrePersist
    @PreUpdate
    private void validate() {
        if (objectType != ObjectType.PURCHASE_ORDER && orderStatus != null) {
            throw new IllegalStateException("Order statusId is only allowed for ORDER object type");
        }
    }
}
