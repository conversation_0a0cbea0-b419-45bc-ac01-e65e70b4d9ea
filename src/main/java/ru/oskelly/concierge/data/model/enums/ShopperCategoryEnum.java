package ru.oskelly.concierge.data.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Категории шоппера (байера)
 */
@Getter
@AllArgsConstructor
public enum ShopperCategoryEnum {
    CLOTHING("Одежда"),
    FOOTWEAR( "Обувь"),
    BAGS_ACCESSORIES("Сумки и аксессуары"),
    WATCHES_JEWELRY("Часы и ювелирные украшения"),
    RARE_LIMITED("Редкие и лимитированные вещи");

    private final String description;
}
