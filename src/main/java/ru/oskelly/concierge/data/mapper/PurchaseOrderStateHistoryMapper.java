package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;
import ru.oskelly.concierge.data.model.PurchaseOrderStateHistory;

/**
 * Mapper для работы с историей состояний заявок на покупку
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface PurchaseOrderStateHistoryMapper {
    PurchaseOrderStateHistory toEntity(PurchaseOrderStateHistoryDTO purchaseOrderStateHistoryDTO);

    PurchaseOrderStateHistoryDTO toDto(PurchaseOrderStateHistory purchaseOrderStateHistory);
}
