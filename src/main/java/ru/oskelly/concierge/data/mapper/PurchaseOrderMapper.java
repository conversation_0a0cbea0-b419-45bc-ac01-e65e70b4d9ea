package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.controller.dto.OrdersForConciergeDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderFullDTO;
import ru.oskelly.concierge.controller.dto.PurchaseOrderListDTO;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE,
        componentModel = MappingConstants.ComponentModel.SPRING,
        uses = ShipmentMapper.class)
public interface PurchaseOrderMapper {
    @Mapping(target = "salesInfo.salesId", source = "salesId")
    @Mapping(target = "sourcerInfo.sourcerId", source = "sourcerId")
    @Mapping(target = "customer.customerId", source = "customerId")
    PurchaseOrderFullDTO toFullDto(PurchaseOrder purchaseOrder, @Context PersonalShopperRepository personalShopperRepository);

    @Mapping(target = "salesId", source = "salesInfo.salesId")
    @Mapping(target = "sourcerId", source = "sourcerInfo.sourcerId")
    @Mapping(target = "customerId", source = "customer.customerId")
    PurchaseOrder toEntity(PurchaseOrderFullDTO dto);

    @Mapping(target = "shipments", qualifiedByName = "toResponseDTOWithoutOffers")
    @Mapping(target = "salesInfo.salesId", source = "salesId")
    @Mapping(target = "sourcerInfo.sourcerId", source = "sourcerId")
    @Mapping(target = "customer.customerId", source = "customerId")
    PurchaseOrderFullDTO toFullDtoWithoutOffers(PurchaseOrder purchaseOrder);

    PurchaseOrderListDTO toListDto(PurchaseOrder purchaseOrder);

    @Mapping(target = "customerId", source = "customerInfo.customerId")
    @Mapping(target = "customerNickName", source = "customerInfo.customerNickName")
    @Mapping(target = "customerPhone", source = "customerInfo.customerPhone")
    @Mapping(target = "salesId", source = "salesInfo.salesId")
    PurchaseOrder toEntity(OrdersForConciergeDTO ordersForConciergeDTO);
}
