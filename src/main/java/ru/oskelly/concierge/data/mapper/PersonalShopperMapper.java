package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Context;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.springframework.util.CollectionUtils;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.bitrix.dto.ShopperResponse;
import ru.oskelly.concierge.controller.dto.PersonalShopperCreateRequest;
import ru.oskelly.concierge.data.model.Brand;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.ShopperCategory;
import ru.oskelly.concierge.data.model.enums.ShopperCategoryEnum;
import ru.oskelly.concierge.data.repository.ShopperCategoryRepository;
import ru.oskelly.concierge.data.model.enums.InteractionType;
import ru.oskelly.concierge.data.model.enums.PaymentFormat;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static ru.oskelly.concierge.data.model.enums.PaymentFormat.POSTPAYMENT;
import static ru.oskelly.concierge.data.model.enums.PaymentFormat.PREPAYMENT;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {Long.class, ZonedDateTime.class})
public interface PersonalShopperMapper {
    /*
    Для всех пришедших id(brands и categories) создаем новые объекты
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "interactionType", source = "interactionType")
    @Mapping(target = "paymentFormat", source = "paymentFormat")
    @Mapping(target = "accessSource", source = "accessSource")
    @Mapping(target = "categories", source = "categories", qualifiedByName = "mapToCategories")
    @Mapping(target = "brands", source = "brands", qualifiedByName = "mapToBrands")
    PersonalShopper toCreateEntity(
            PersonalShopperCreateRequest request,
            @Context ShopperCategoryRepository shopperCategoryRepository
    );

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", expression = "java(Long.valueOf(shopper.oskellyUserId()))")
    @Mapping(target = "bitrixId", source = "id")
    @Mapping(target = "nickname", source = "oskellyProfile")
    @Mapping(target = "name", source = "title")
    @Mapping(target = "interactionType", source = "interactionType", qualifiedByName = "mapInteractionType")
    @Mapping(target = "priority", source = "priority", qualifiedByName = "mapPriority")
    @Mapping(target = "paymentFormat", source = "paymentFormat", qualifiedByName = "mapPaymentFormat")
    PersonalShopper createEntityFromBitrix(ShopperResponse shopper);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", expression = "java(Long.valueOf(bitrixShopper.oskellyUserId()))")
    @Mapping(target = "bitrixId", source = "id")
    @Mapping(target = "nickname", source = "oskellyProfile")
    @Mapping(target = "name", source = "title")
    @Mapping(target = "interactionType", source = "interactionType", qualifiedByName = "mapInteractionType")
    @Mapping(target = "priority", source = "priority", qualifiedByName = "mapPriority")
    @Mapping(target = "paymentFormat", source = "paymentFormat", qualifiedByName = "mapPaymentFormat")
    void updateEntityFromBitrix(@MappingTarget PersonalShopper shopper, ShopperResponse bitrixShopper);

    /*
    Коды полей в битриксе:
        38 - нужна предоплата
        39 - не нужна предоплата
    */
    @Named("mapPaymentFormat")
    default PaymentFormat mapPaymentFormat(String paymentFormatCode) {
        if ("38".equalsIgnoreCase(paymentFormatCode)) {
            return PREPAYMENT;
        } else if ("39".equalsIgnoreCase(paymentFormatCode)) {
            return POSTPAYMENT;
        } else {
            return null;
        }
    }

    /*
    Коды полей в битриксе:
        40 - есть приоритет
        41 - нет приоритета
    */
    @Named("mapPriority")
    default boolean mapPriority(String priorityCode) {
        return "40".equalsIgnoreCase(priorityCode);
    }

    /*
    Коды полей в битриксе:
        26 - Интернет сайты
        27 - Мультибренд
        28 - Под запрос
        29 - Универсал
    */
    @Named("mapInteractionType")
    default Set<InteractionType> mapInteractionType(List<Integer> interactionType) {
        if (interactionType == null || interactionType.isEmpty()) {
            return Collections.emptySet();
        }

        return interactionType.stream()
                .map(code -> switch (code) {
                    case 26 -> InteractionType.INTERNET_SITES;
                    case 27 -> InteractionType.MULTIBRAND;
                    case 28 -> InteractionType.ON_REQUEST;
                    case 29 -> InteractionType.UNIVERSAL;
                    default -> throw new IllegalArgumentException("Unknown interactionType code: " + code);
                })
                .collect(Collectors.toSet());
    }

    @Named("mapToBrands")
    default Set<Brand> mapToBrands(Set<Long> brandIds) {
        if (brandIds == null || brandIds.isEmpty()) {
            return Collections.emptySet();
        }
        return brandIds.stream()
                .map(id -> {
                    Brand brand = new Brand();
                    brand.setBrandId(id);
                    return brand;
                })
                .collect(Collectors.toSet());
    }

    @Named("mapToCategories")
    default Set<ShopperCategory> mapToCategories(
        Set<ShopperCategoryEnum> codes,
        @Context ShopperCategoryRepository repository
    ) {
        return CollectionUtils.isEmpty(codes)
            ? Collections.emptySet()
            : repository.findByCodeIn(codes);
    }
}