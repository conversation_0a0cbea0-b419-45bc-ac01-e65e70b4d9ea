package ru.oskelly.concierge.data.mapper;

import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;

import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface ProposedOfferMapper {

    @Mapping(target = "offerId", source = "offer.id")
    @Mapping(target = "isCompleteSet", source = "completeSet")
    @Mapping(target = "productCondition", source = "productConditionId", qualifiedByName = "idToProductConditionDTO")
    ProposedOfferDTO toDto(ProposedOffer proposedOffer);

    @Mapping(target = "offer", ignore = true)
    @Mapping(target = "productConditionId", source = "productCondition.id")
    ProposedOffer toEntity(ProposedOfferDTO dto, @Context Offer offer);

    Set<ProposedOffer> toEntityList(Set<ProposedOfferDTO> dtos, @Context Offer offer);

    List<ProposedOfferDTO> toDtoList(List<ProposedOffer> proposedOffers);

    Set<ProposedOfferDTO> toProposedOfferDTOSet(Set<ProposedOffer> proposedOffers);
    Set<ProposedOffer> toProposedOfferEntitySet(Set<ProposedOfferDTO> dtos);

    @AfterMapping
    default void setOffer(@MappingTarget ProposedOffer entity, @Context Offer offer) {
        if (offer != null) {
            entity.setOffer(offer);
        }
    }

    @Named("idToOffer")
    default Offer idToOffer(Long id) {
        if (id == null) return null;
        Offer offer = new Offer();
        offer.setId(id);
        return offer;
    }

    default Long offerToId(Offer offer) {
        return offer != null ? offer.getId() : null;
    }

    @Named("idToProductConditionDTO")
    default ProductConditionDTO idToProductConditionDTO(Long id) {
        if (id == null) return null;
        return new ProductConditionDTO(id, null, null);
    }
}
