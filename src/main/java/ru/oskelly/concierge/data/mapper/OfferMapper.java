package ru.oskelly.concierge.data.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import ru.oskelly.concierge.bitrix.dto.OfferCreateFields;
import ru.oskelly.concierge.bitrix.dto.OfferCreateRequest;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.OfferDTO;
import ru.oskelly.concierge.controller.dto.ProductPlatformDTO;
import ru.oskelly.concierge.controller.dto.ProposedOfferDTO;
import ru.oskelly.concierge.controller.mercaux.dto.ShopperInfoDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.repository.PersonalShopperRepository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface OfferMapper {

    @Mapping(target = "comparisonCriteria", ignore = true)
    @Mapping(target = "id", source = "offer.id")
    @Mapping(target = "shipmentId", source = "offer.shipment.id")
    @Mapping(target = "seller.sellerId", source = "offer.sellerId")
    @Mapping(target = "shopper", source = "offer", qualifiedByName = "mapShopperInfo")
    @Mapping(target = "product", source = "offer", qualifiedByName = "mapProduct")
    @Mapping(target = "type", source = "offer.type")
    @Mapping(target = "sellerType", source = "offer.sellerType")
    @Mapping(target = "creationDate", source = "offer.creationDate")
    @Mapping(target = "buyerOffers.proposedOffers", source = "offer.proposedOffers")
    OfferDTO toDTO(Offer offer,
                   @Context PersonalShopperRepository personalShopperRepository);

    @Named("mapShopperInfo")
    default ShopperInfoDTO mapShopperInfo(Offer offer, @Context PersonalShopperRepository personalShopperRepository) {
        if (offer.getSellerId() == null) {
            return new ShopperInfoDTO(null, null, null, null, null, null, Collections.emptySet());
        }

        // Один запрос к БД для получения PersonalShopper
        Optional<PersonalShopper> personalShopperOpt = personalShopperRepository.findByUserId(offer.getSellerId());

        // Если sellerType не BUYER, возвращаем только базовую информацию
        if (!"BUYER".equals(offer.getSellerType())) {
            return new ShopperInfoDTO(
                    null,
                    offer.getSellerId(),
                    null,
                    null,
                    null,
                    null,
                    Collections.emptySet()
            );
        }

        // Если PersonalShopper не найден, возвращаем базовую информацию
        if (personalShopperOpt.isEmpty()) {
            return new ShopperInfoDTO(
                    null,
                    offer.getSellerId(),
                    null,
                    null,
                    null,
                    null,
                    Collections.emptySet()
            );
        }

        // Используем найденный PersonalShopper для заполнения всех полей
        PersonalShopper personalShopper = personalShopperOpt.get();

        return new ShopperInfoDTO(
                personalShopper.getId(),
                personalShopper.getUserId(),
                personalShopper.getNickname(),
                personalShopper.getName(),
                null, // urlAvatar - нет в PersonalShopper
                personalShopper.getPaymentFormat() != null ?
                        new DescriptionStructureEnum(personalShopper.getPaymentFormat().name(), personalShopper.getPaymentFormat().getDescription()) : null,
                personalShopper.getInteractionType() != null ?
                        personalShopper.getInteractionType().stream()
                                .map(it -> new DescriptionStructureEnum(it.name(), it.getValue()))
                                .collect(Collectors.toSet()) :
                        Collections.emptySet()
        );
    }

    @Mapping(target = "purchasePlatformStatus", ignore = true)
    @Mapping(target = "isSentToCustomer", ignore = true)
    @Mapping(target = "userId", expression = "java(ru.oskelly.concierge.common.ThreadLocalContext.get(ru.oskelly.concierge.common.ContextConstants.USER_ID, Long.class))")
    @Mapping(target = "id", source = "offerDTO.id")
    @Mapping(target = "shipment", expression = "java(createShipmentFromId(offerDTO.shipmentId()))")
    @Mapping(target = "sellerId", source = "offerDTO.seller.sellerId")
    @Mapping(target = "productId", source = "offerDTO.product.productId")
    @Mapping(target = "type", source = "offerDTO.type")
    @Mapping(target = "sellerType", source = "offerDTO.sellerType")
    @Mapping(target = "proposedOffers", source = "offerDTO.buyerOffers.proposedOffers")
    Offer toEntity(OfferDTO offerDTO);


    // Mapping для ProposedOffer
    @Mapping(target = "id", source = "proposedOffer.id")
    @Mapping(target = "rublePrice", source = "proposedOffer.rublePrice")
    @Mapping(target = "deliveryDate", source = "proposedOffer.deliveryDate")
    @Mapping(target = "validUntil", source = "proposedOffer.validUntil")
    @Mapping(target = "creationDate", source = "proposedOffer.creationDate")
    @Mapping(target = "currency", source = "proposedOffer.currency")
    @Mapping(target = "currencyPrice", source = "proposedOffer.currencyPrice")
    @Mapping(target = "hasReceipt", source = "proposedOffer.hasReceipt")
    @Mapping(target = "isCompleteSet", source = "proposedOffer.completeSet")
    @Mapping(target = "hasCustomCommission", source = "proposedOffer.hasCustomCommission")
    @Mapping(target = "commission", source = "proposedOffer.commission")
    @Mapping(target = "comment", source = "proposedOffer.comment")
    @Mapping(target = "orderId", source = "proposedOffer.orderId")
    @Mapping(target = "proposedProductId", source = "proposedOffer.proposedProductId")
    @Mapping(target = "offerId", source = "proposedOffer.offer.id")
    @Mapping(target = "productCondition.id", source = "proposedOffer.productConditionId")
    ProposedOfferDTO toProposedOfferDTO(ProposedOffer proposedOffer);

    @Mapping(target = "id", source = "dto.id")
    @Mapping(target = "rublePrice", source = "dto.rublePrice")
    @Mapping(target = "deliveryDate", source = "dto.deliveryDate")
    @Mapping(target = "validUntil", source = "dto.validUntil")
    @Mapping(target = "creationDate", source = "dto.creationDate")
    @Mapping(target = "currency", source = "dto.currency")
    @Mapping(target = "currencyPrice", source = "dto.currencyPrice")
    @Mapping(target = "hasReceipt", source = "dto.hasReceipt")
    @Mapping(target = "isCompleteSet", source = "dto.isCompleteSet")
    @Mapping(target = "hasCustomCommission", source = "dto.hasCustomCommission")
    @Mapping(target = "commission", source = "dto.commission")
    @Mapping(target = "comment", source = "dto.comment")
    @Mapping(target = "orderId", source = "dto.orderId")
    @Mapping(target = "proposedProductId", source = "dto.proposedProductId")
    @Mapping(target = "offer", expression = "java(createOfferFromId(dto.offerId()))")
    @Mapping(target = "productConditionId", source = "dto.productCondition.id")
    @Mapping(target = "isSentToCustomer", ignore = true)
    ProposedOffer toProposedOfferEntity(ProposedOfferDTO dto);

    // Метод для обновления Offer с использованием update-мапперов для вложенных сущностей
    @Mapping(target = "purchasePlatformStatus", ignore = true)
    @Mapping(target = "shipment", expression = "java(createShipmentFromId(offerDTO.shipmentId()))")
    @Mapping(target = "sellerId", source = "seller.sellerId")
    @Mapping(target = "productId", source = "offerDTO.product.productId")
    @Mapping(target = "userId", ignore = true)
    @Mapping(target = "isSentToCustomer", ignore = true)
    @Mapping(target = "proposedOffers", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
            nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    void updateToEntity(OfferDTO offerDTO, @MappingTarget Offer target);

    // Методы для работы с коллекциями
    List<Offer> toEntityList(List<OfferDTO> offerDTOs);

    List<OfferDTO> toDTOList(List<Offer> offers, @Context PersonalShopperRepository personalShopperRepository);

    @Mapping(target = "fields", expression = "java(createFields(bitrixDealId, companyId))")
    OfferCreateRequest toCreateEmptyRequest(OfferDTO dto, Long bitrixDealId, String companyId);

    Set<ProposedOfferDTO> toProposedOfferDTOSet(Set<ProposedOffer> proposedOffers);

    Set<ProposedOffer> toProposedOfferEntitySet(Set<ProposedOfferDTO> dtos);

    default OfferCreateFields createFields(Long bitrixDealId, String companyId) {
        return OfferCreateFields.builder()
                .dealId(String.valueOf(bitrixDealId))
                .companyId(companyId)
                .build();
    }

    default Shipment createShipmentFromId(Long shipmentId) {
        if (shipmentId == null) {
            return null;
        }
        Shipment shipment = new Shipment();
        shipment.setId(shipmentId);
        return shipment;
    }

    default Offer createOfferFromId(Long offerId) {
        if (offerId == null) {
            return null;
        }
        Offer offer = new Offer();
        offer.setId(offerId);
        return offer;
    }

    @Named("mapProduct")
    default ProductPlatformDTO mapProduct(Offer offer) {
        if (offer.getProductId() == null) {
            return null;
        }
        return new ProductPlatformDTO(
                offer.getProductId(),
                null, // currencyPrice
                null, // productPhoto
                null, // productLocation
                null, // brand
                null, // productCategory
                null, // availableSizes
                null, // priceWithoutDiscount
                null, // priceWithDiscount
                null, // discountAmount
                null, // sizeType
                null, // conditionId
                null, // conditionName
                null, // productState
                null, // likesCount
                null, // isLiked
                null, // url
                null, // discount
                null  // currency
        );
    }
}
