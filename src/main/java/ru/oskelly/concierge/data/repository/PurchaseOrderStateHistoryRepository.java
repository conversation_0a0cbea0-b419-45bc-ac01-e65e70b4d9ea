package ru.oskelly.concierge.data.repository;

import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.PurchaseOrderStateHistory;
import ru.oskelly.concierge.data.model.QPurchaseOrderStateHistory;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.List;

/**
 * Репозиторий работы с историей состояний заявок на покупку
 */
@Repository
public interface PurchaseOrderStateHistoryRepository extends BaseRepository<PurchaseOrderStateHistory, QPurchaseOrderStateHistory, Long> {
    List<PurchaseOrderStateHistory> findByPurchaseOrderIdOrderByTransitionDateDesc(Long purchaseOrderId);

    boolean existsByPurchaseOrderIdAndTargetState(Long purchaseOrderId, PurchaseOrderStatusEnum targetState);
}
