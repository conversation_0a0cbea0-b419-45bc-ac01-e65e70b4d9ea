package ru.oskelly.concierge.data.repository;

import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.QShopperCategory;
import ru.oskelly.concierge.data.model.ShopperCategory;
import ru.oskelly.concierge.data.model.enums.ShopperCategoryEnum;

import java.util.Set;

@Repository
public interface ShopperCategoryRepository extends BaseRepository<ShopperCategory, QShopperCategory, Long> {
    Set<ShopperCategory> findByCodeIn(Set<ShopperCategoryEnum> codes);
}
