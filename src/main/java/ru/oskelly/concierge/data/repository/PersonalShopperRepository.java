package ru.oskelly.concierge.data.repository;

import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.QPersonalShopper;

import java.util.List;

import java.util.Optional;

@Repository
public interface PersonalShopperRepository extends BaseRepository<PersonalShopper, QPersonalShopper,Long> {
    boolean existsByUserId(Long userId);
    Optional<PersonalShopper> findByUserId(Long userId);
    List<PersonalShopper> findAllByUserIdIn(List<Long> userIds);
}
