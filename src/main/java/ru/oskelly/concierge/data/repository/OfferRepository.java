package ru.oskelly.concierge.data.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.QOffer;

import java.util.List;

@Repository
public interface OfferRepository extends BaseRepository<Offer, QOffer, Long> {

    long countByShipment_Id(Long id);

    long countByShipment_IdAndProductIdNotNull(Long shipmentId);

    long countByShipment_IdAndProductIdNull(Long id);

    List<Offer> findByShipment_IdAndProductIdNull(Long id);

    List<Offer> findByShipment_IdAndProductIdNotNull(Long id);

    @Query("SELECT DISTINCT po.id FROM Offer o JOIN o.shipment s JOIN s.purchaseOrder po WHERE o IN :offers")
    List<Long> findOrderIdsByOffers(@Param("offers") List<Offer> offers);

    @Query("""
        SELECT DISTINCT po.id FROM ProposedOffer pro
            JOIN pro.offer o
            JOIN o.shipment s
            JOIN s.purchaseOrder po
            WHERE pro IN :offers
    """)
    List<Long> findOrderIdsByProposedOffers(@Param("offers") List<ProposedOffer> offers);
}
