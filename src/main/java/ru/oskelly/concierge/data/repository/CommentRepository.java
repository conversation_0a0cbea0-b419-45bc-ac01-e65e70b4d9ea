package ru.oskelly.concierge.data.repository;

import org.springframework.data.jpa.repository.Query;
import ru.oskelly.concierge.data.model.Comment;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.QComment;

import java.util.List;
import java.util.Optional;

/**
 * Репозиторий работы с комментариями к заявкам на покупку
 */
public interface CommentRepository extends BaseRepository<Comment, QComment, Long> {
  /**
   * Запрос по комментариям с учетом закрепленных комментариев
   * @param purchaseOrder - заявка по которой ищем комментарии
   * @return - список комментариев
   */
  @Query("select c from Comment c " +
          "where c.purchaseOrder = :purchaseOrder " +
          "order by c.isPined nulls last, c.datetime desc")
  List<Comment> findAllByPurchaseOrder(PurchaseOrder purchaseOrder);

  Optional<Comment> findByIdAndPurchaseOrder_Id(Long id, Long purchaseOrderId);
}