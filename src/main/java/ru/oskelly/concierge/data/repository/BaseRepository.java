package ru.oskelly.concierge.data.repository;

import com.querydsl.core.types.dsl.EntityPathBase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.io.Serializable;

/**
 * Общий для всех репозиториев интерфейс, переопределяет методы из GrupRepository и добавляет возможность использования Querydsl
 * @param <T> - сущность репозитория
 * @param <P> - путь к сгенерированной сущности Q
 * @param <ID> - тип идентификатора первичного ключа
 */
@NoRepositoryBean
public interface BaseRepository<T, P extends EntityPathBase<T>, ID extends Serializable>
        extends JpaRepository<T, ID>, QuerydslPredicateExecutor<T>, QuerydslBinderCustomizer<P>, PagingAndSortingRepository<T, ID> {
    @Override
    default void customize(QuerydslBindings bindings, P root) {

    }
}
