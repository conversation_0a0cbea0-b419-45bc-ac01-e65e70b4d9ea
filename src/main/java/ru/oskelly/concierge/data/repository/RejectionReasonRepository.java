package ru.oskelly.concierge.data.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.QRejectionReason;
import ru.oskelly.concierge.data.model.RejectionReason;
import ru.oskelly.concierge.data.model.enums.ObjectType;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;

import java.util.List;

/**
 * Репозиторий для работы с причинами отклонений
 */
@Repository
public interface RejectionReasonRepository extends BaseRepository<RejectionReason, QRejectionReason, Long> {

    /**
     * Поиск причин отклонения по типу объекта и статусу заявки
     *
     * @param objectType тип объекта (обязательный)
     * @param status     статус заявки (только для ORDER)
     * @return список подходящих причин отклонения
     */
    @Query("SELECT rr FROM RejectionReason rr WHERE " +
            "rr.objectType = :objectType AND rr.orderStatus = :status")
    List<RejectionReason> findByObjectTypeAndStatus(
            @Param("objectType") ObjectType objectType,
            @Param("status") PurchaseOrderStatusEnum status);

    /**
     * Поиск причини отклонения по типу объекта
     *
     * @param objectType тип объекта (обязательный)
     * @return список подходящих причини отклонения
     */
    List<RejectionReason> findByObjectType(ObjectType objectType);
}

