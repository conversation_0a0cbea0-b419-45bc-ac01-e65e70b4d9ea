package ru.oskelly.concierge.bitrix.dto;

public class BitrixFields {
    public final static String ALL = "*";
    public final static String YES = "Y";
    public final static String NO = "N";
    public final static String ALL_CUSTOM_FIELDS = "UF_*";
    public final static String ID = "ID";
    public final static String OPENED = "OPENED";;
    public final static String NAME = "NAME";
    public final static String PHONE = "PHONE";
    public final static String MOBILE = "MOBILE";
    public final static String EMAIL = "EMAIL";
    public final static String DATE_CREATE = "DATE_CREATE";
    public final static String VALUE = "VALUE";
    public final static String VALUE_TYPE = "VALUE_TYPE";
    public final static String CATEGORY_ID = "CATEGORY_ID";
}