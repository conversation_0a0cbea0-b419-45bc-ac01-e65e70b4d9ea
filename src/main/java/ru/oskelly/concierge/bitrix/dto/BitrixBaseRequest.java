package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Builder
public record BitrixBaseRequest(
        @JsonProperty("FILTER") Map<String, Object> filter,
        @JsonProperty("SELECT") List<String> select,
        @JsonProperty("FIELDS") Map<String, Object> fields
) {

    public BitrixBaseRequest {
        filter = filter != null ? filter : new HashMap<>();
        select = select != null ? select : new ArrayList<>();
        fields = fields != null ? fields : new HashMap<>();
    }

    public void addFilter(String filterName, Object value) {
        if (value != null) {
            filter.put(filterName, value);
        }
    }

    public void addField(String fieldName, Object value) {
        if (value != null) {
            fields.put(fieldName, value);
        }
    }

}