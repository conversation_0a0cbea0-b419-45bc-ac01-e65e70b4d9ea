package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * Базовый шаблон ответа от Bitrix
 * @param <T> тело ответа конкретного запроса
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BitrixBaseResponse<T> {
    private T result;
    private Integer next;
    private TimeInfo time;

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TimeInfo {
        private Double start;
        private Double finish;
        private Double duration;
        private Double processing;
        private String date_start;
        private String date_finish;
        private Integer operating;
    }
}
