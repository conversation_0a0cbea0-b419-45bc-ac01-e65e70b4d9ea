package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class BitrixContactDTO {

    @JsonProperty("ID")
    private Long id;

    @JsonProperty("NAME")
    private String name;

    @JsonProperty("SOURCE_ID")
    private String sourceId;

    @JsonProperty("SOURCE_DESCRIPTION")
    private String sourceDescription;

    @JsonProperty("ASSIGNED_BY_ID")
    private Long assignedById;

    @JsonProperty("PHONE")
    private List<BitrixPhoneDTO> phones;

    @JsonProperty("EMAIL")
    private List<BitrixPhoneDTO> emails;
}
