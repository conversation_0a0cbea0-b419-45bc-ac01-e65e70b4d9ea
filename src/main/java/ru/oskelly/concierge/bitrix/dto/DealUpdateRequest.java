package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;


/**
 * DTO обновления сделки Bitrix.
 */
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public record DealUpdateRequest(
        @JsonProperty("ID")
        Long id,
        @JsonProperty("FIELDS")
        DealFields fields
) {
}
