package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * DTO шоппера из битрикс.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public record ShopperResponse(
        @JsonProperty("ID") String id,
        @JsonProperty("COMPANY_TYPE") String companyType,
        @JsonProperty("TITLE") String title,
        @JsonProperty("LOGO") String logo,
        @JsonProperty("LEAD_ID") String leadId,
        @JsonProperty("HAS_PHONE") String hasPhone,
        @JsonProperty("HAS_EMAIL") String hasEmail,
        @JsonProperty("HAS_IMOL") String hasImol,
        @JsonProperty("ASSIGNED_BY_ID") String assignedById,
        @JsonProperty("CREATED_BY_ID") String createdById,
        @JsonProperty("MODIFY_BY_ID") String modifyById,
        @JsonProperty("BANKING_DETAILS") String bankingDetails,
        @JsonProperty("INDUSTRY") String industry,
        @JsonProperty("REVENUE") String revenue,
        @JsonProperty("CURRENCY_ID") String currencyId,
        @JsonProperty("EMPLOYEES") String employees,
        @JsonProperty("COMMENTS") String comments,
        @JsonProperty("DATE_CREATE") OffsetDateTime dateCreate,
        @JsonProperty("DATE_MODIFY") OffsetDateTime dateModify,
        @JsonProperty("OPENED") String opened,
        @JsonProperty("IS_MY_COMPANY") String isMyCompany,
        @JsonProperty("ORIGINATOR_ID") String originatorId,
        @JsonProperty("ORIGIN_ID") String originId,
        @JsonProperty("ORIGIN_VERSION") String originVersion,
        @JsonProperty("LAST_ACTIVITY_TIME") OffsetDateTime lastActivityTime,
        @JsonProperty("ADDRESS") String address,
        @JsonProperty("ADDRESS_2") String address2,
        @JsonProperty("ADDRESS_CITY") String addressCity,
        @JsonProperty("ADDRESS_POSTAL_CODE") String addressPostalCode,
        @JsonProperty("ADDRESS_REGION") String addressRegion,
        @JsonProperty("ADDRESS_PROVINCE") String addressProvince,
        @JsonProperty("ADDRESS_COUNTRY") String addressCountry,
        @JsonProperty("ADDRESS_COUNTRY_CODE") String addressCountryCode,
        @JsonProperty("ADDRESS_LOC_ADDR_ID") String addressLocAddrId,
        @JsonProperty("ADDRESS_LEGAL") String addressLegal,
        @JsonProperty("REG_ADDRESS") String regAddress,
        @JsonProperty("REG_ADDRESS_2") String regAddress2,
        @JsonProperty("REG_ADDRESS_CITY") String regAddressCity,
        @JsonProperty("REG_ADDRESS_POSTAL_CODE") String regAddressPostalCode,
        @JsonProperty("REG_ADDRESS_REGION") String regAddressRegion,
        @JsonProperty("REG_ADDRESS_PROVINCE") String regAddressProvince,
        @JsonProperty("REG_ADDRESS_COUNTRY") String regAddressCountry,
        @JsonProperty("REG_ADDRESS_COUNTRY_CODE") String regAddressCountryCode,
        @JsonProperty("REG_ADDRESS_LOC_ADDR_ID") String regAddressLocAddrId,
        @JsonProperty("UTM_SOURCE") String utmSource,
        @JsonProperty("UTM_MEDIUM") String utmMedium,
        @JsonProperty("UTM_CAMPAIGN") String utmCampaign,
        @JsonProperty("UTM_CONTENT") String utmContent,
        @JsonProperty("UTM_TERM") String utmTerm,
        @JsonProperty("LAST_ACTIVITY_BY") String lastActivityBy,
        @JsonProperty("UF_CRM_1699882896995") List<Integer> interactionType,
        @JsonProperty("UF_CRM_1699882994739") List<Integer> ufCrm1699882994739,
        @JsonProperty("UF_CRM_1699883181318") String paymentFormat,
        @JsonProperty("UF_CRM_1699883240467") String priority,
        @JsonProperty("UF_CRM_1699883613645") String ufCrm1699883613645,
        @JsonProperty("UF_CRM_1701695082664") String ufCrm1701695082664,
        @JsonProperty("UF_CRM_OLCHATWASTATS") String ufCrmOlChatWaStats,
        @JsonProperty("UF_CRM_1750236896282") String oskellyUserId,
        @JsonProperty("UF_CRM_OSKELLY_PROFILE") String oskellyProfile
) {
}
