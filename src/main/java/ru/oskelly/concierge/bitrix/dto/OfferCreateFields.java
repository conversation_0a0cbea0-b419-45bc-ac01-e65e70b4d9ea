package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public record OfferCreateFields(
        @JsonProperty("DEAL_ID")
        String dealId,

        @JsonProperty("COMPANY_ID")
        String companyId,

        @JsonProperty("UF_CRM_QUOTE_1749460483607")
        String purchaseCurrency,

        @JsonProperty("UF_CRM_QUOTE_1749624452254")
        String handoverPriceForeign,

        @JsonProperty("UF_CRM_QUOTE_1699880725793")
        String exchangeRateToRub,

        @JsonProperty("UF_CRM_QUOTE_1749624439778")
        String handoverPriceRub,

        @JsonProperty("UF_CRM_QUOTE_1749460503035")
        String oskellyCommission,

        @JsonProperty("UF_CRM_QUOTE_1749460524114")
        String deliveryToOskellyOfficeDate,

        @JsonProperty("UF_CRM_QUOTE_1749460547947")
        String comment,

        @JsonProperty("UF_CRM_QUOTE_1749460582726")
        String offerExpirationDate,

        @JsonProperty("UF_CRM_QUOTE_1749460593364")
        String receiptProvided,

        @JsonProperty("UF_CRM_QUOTE_1749460609357")
        String completeSet,

        @JsonProperty("UF_CRM_QUOTE_1749460615908")
        String conditionStatus,

        @JsonProperty("UF_CRM_QUOTE_1749460652796")
        String platformPriceForeign,

        @JsonProperty("UF_CRM_QUOTE_1749460633858")
        String platformPriceRub
) {
}
