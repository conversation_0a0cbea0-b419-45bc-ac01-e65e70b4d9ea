package ru.oskelly.concierge.bitrix.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFieldsResponse {

    @JsonProperty("ID")
    private String id;

    @JsonProperty("XML_ID")
    private String xmlId;

    @JsonProperty("ACTIVE")
    private String active;

    @JsonProperty("NAME")
    private String name;

    @JsonProperty("LAST_NAME")
    private String lastName;

    @JsonProperty("SECOND_NAME")
    private String secondName;

    @JsonProperty("TITLE")
    private String title;

    @JsonProperty("EMAIL")
    private String email;

    @JsonProperty("LAST_LOGIN")
    private String lastLogin;

    @JsonProperty("DATE_REGISTER")
    private String dateRegister;

    @JsonProperty("TIME_ZONE")
    private String timeZone;

    @JsonProperty("IS_ONLINE")
    private String isOnline;

    @JsonProperty("TIME_ZONE_OFFSET")
    private String timeZoneOffset;

    @JsonProperty("TIMESTAMP_X")
    private String timestampX;

    @JsonProperty("LAST_ACTIVITY_DATE")
    private String lastActivityDate;

    @JsonProperty("PERSONAL_GENDER")
    private String personalGender;

    @JsonProperty("PERSONAL_PROFESSION")
    private String personalProfession;

    @JsonProperty("PERSONAL_WWW")
    private String personalWww;

    @JsonProperty("PERSONAL_BIRTHDAY")
    private String personalBirthday;

    @JsonProperty("PERSONAL_PHOTO")
    private String personalPhoto;

    @JsonProperty("PERSONAL_ICQ")
    private String personalIcq;

    @JsonProperty("PERSONAL_PHONE")
    private String personalPhone;

    @JsonProperty("PERSONAL_FAX")
    private String personalFax;

    @JsonProperty("PERSONAL_MOBILE")
    private String personalMobile;

    @JsonProperty("PERSONAL_PAGER")
    private String personalPager;

    @JsonProperty("PERSONAL_STREET")
    private String personalStreet;

    @JsonProperty("PERSONAL_CITY")
    private String personalCity;

    @JsonProperty("PERSONAL_STATE")
    private String personalState;

    @JsonProperty("PERSONAL_ZIP")
    private String personalZip;

    @JsonProperty("PERSONAL_COUNTRY")
    private String personalCountry;

    @JsonProperty("PERSONAL_MAILBOX")
    private String personalMailbox;

    @JsonProperty("PERSONAL_NOTES")
    private String personalNotes;

    @JsonProperty("WORK_PHONE")
    private String workPhone;

    @JsonProperty("WORK_COMPANY")
    private String workCompany;

    @JsonProperty("WORK_POSITION")
    private String workPosition;

    @JsonProperty("WORK_DEPARTMENT")
    private String workDepartment;

    @JsonProperty("WORK_WWW")
    private String workWww;

    @JsonProperty("WORK_FAX")
    private String workFax;

    @JsonProperty("WORK_PAGER")
    private String workPager;

    @JsonProperty("WORK_STREET")
    private String workStreet;

    @JsonProperty("WORK_MAILBOX")
    private String workMailbox;

    @JsonProperty("WORK_CITY")
    private String workCity;

    @JsonProperty("WORK_STATE")
    private String workState;

    @JsonProperty("WORK_ZIP")
    private String workZip;

    @JsonProperty("WORK_COUNTRY")
    private String workCountry;

    @JsonProperty("WORK_PROFILE")
    private String workProfile;

    @JsonProperty("WORK_LOGO")
    private String workLogo;

    @JsonProperty("WORK_NOTES")
    private String workNotes;

    @JsonProperty("UF_SKYPE_LINK")
    private String ufSkypeLink;

    @JsonProperty("UF_ZOOM")
    private String ufZoom;

    @JsonProperty("UF_EMPLOYMENT_DATE")
    private String ufEmploymentDate;

    @JsonProperty("UF_TIMEMAN")
    private String ufTimeman;

    @JsonProperty("UF_DEPARTMENT")
    private String ufDepartment;

    @JsonProperty("UF_INTERESTS")
    private String ufInterests;

    @JsonProperty("UF_SKILLS")
    private String ufSkills;

    @JsonProperty("UF_WEB_SITES")
    private String ufWebSites;

    @JsonProperty("UF_XING")
    private String ufXing;

    @JsonProperty("UF_LINKEDIN")
    private String ufLinkedin;

    @JsonProperty("UF_FACEBOOK")
    private String ufFacebook;

    @JsonProperty("UF_TWITTER")
    private String ufTwitter;

    @JsonProperty("UF_SKYPE")
    private String ufSkype;

    @JsonProperty("UF_DISTRICT")
    private String ufDistrict;

    @JsonProperty("UF_PHONE_INNER")
    private String ufPhoneInner;

    @JsonProperty("USER_TYPE")
    private String userType;
}
