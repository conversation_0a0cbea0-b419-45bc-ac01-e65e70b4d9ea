package ru.oskelly.concierge.bitrix.config;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import ru.oskelly.concierge.exception.BitrixClientException;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class BitrixErrorDecoder implements ErrorDecoder {

    // todo: доработать декодирование ошибок
    @Override
    public Exception decode(String methodKey, Response response) {
        String body = null;

        try {
            if (response.body() != null) {
                body = new String(response.body().asInputStream().readAllBytes(), StandardCharsets.UTF_8);
            }
        } catch (IOException ignored) {
            log.debug("Can't read response body for method: {}", methodKey);
        }

        return new BitrixClientException(
            String.format("Bitrix error [%s]: status %s, body: %s", methodKey, response.status(), body)
        );
    }
}
