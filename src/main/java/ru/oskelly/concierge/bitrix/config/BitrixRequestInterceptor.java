package ru.oskelly.concierge.bitrix.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;

public class BitrixRequestInterceptor implements RequestInterceptor {

    @Value("${bitrix.webhook}")
    private String webhook;

    @Value("${bitrix.user-id}")
    private Long userId;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String action = requestTemplate.url().replaceAll("/", "");
        String updatedUrl = String.format("/rest/%s/%s/%s", userId, webhook, action);
        requestTemplate.uri(updatedUrl, false);
    }
}
