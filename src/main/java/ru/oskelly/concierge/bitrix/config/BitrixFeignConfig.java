package ru.oskelly.concierge.bitrix.config;

import feign.RequestInterceptor;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BitrixFeignConfig {

    @Bean
    public ErrorDecoder bitrixFeignErrorDecoder() {
        return new BitrixErrorDecoder();
    }

    @Bean
    public RequestInterceptor bitrixRequestInterceptor() {
        return new BitrixRequestInterceptor();
    }
}
