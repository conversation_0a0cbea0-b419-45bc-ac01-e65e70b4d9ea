package ru.oskelly.concierge.exception;

/**
 * Исключение для ненайденных proposed offer
 */
public class ProposedOfferNotFoundException extends ConciergeAbstractException {
    public ProposedOfferNotFoundException(String message, Throwable cause, Long orderId, Integer httpCode) {
        super(message, cause, orderId, httpCode);
    }
    public ProposedOfferNotFoundException(String message, Integer httpCode) {
        super(message, null, null, httpCode);
    }
}
