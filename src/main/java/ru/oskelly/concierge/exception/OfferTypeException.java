package ru.oskelly.concierge.exception;

/**
 * Ошибка, возникающая при взаимодействии с некорректным типом предложения
 */
public class OfferTypeException extends ConciergeAbstractException {
    public OfferTypeException(String message, Throwable cause, Long orderId, Integer httpCode) {
        super(message, cause, orderId, httpCode);
    }
    public OfferTypeException(String message, Integer httpCode) {
        super(message, null, null, httpCode);
    }
}
