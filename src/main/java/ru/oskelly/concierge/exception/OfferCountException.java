package ru.oskelly.concierge.exception;

/**
 * Ошибка, возникающая при некорректном количестве предложений
 */
public class OfferCountException extends ConciergeAbstractException {
    public OfferCountException(String message, Throwable cause, Long orderId, Integer httpCode) {
        super(message, cause, orderId, httpCode);
    }
    public OfferCountException(String message, Integer httpCode) {
        super(message, null, null, httpCode);
    }
}
