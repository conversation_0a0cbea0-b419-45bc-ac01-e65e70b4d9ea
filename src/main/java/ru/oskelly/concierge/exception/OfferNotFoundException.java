package ru.oskelly.concierge.exception;

/**
 * Исключение для ненайденных предложений
 */
public class OfferNotFoundException extends ConciergeAbstractException {
    public OfferNotFoundException(String message, Throwable cause, Long orderId, Integer httpCode) {
        super(message, cause, orderId, httpCode);
    }
    public OfferNotFoundException(String message, Integer httpCode) {
        super(message, null, null, httpCode);
    }
}
