package ru.oskelly.concierge.exception;

import lombok.Getter;

import java.util.Map;

/**
 * Исключение для ошибок валидации
 */
@Getter
public class ValidationException extends ConciergeAbstractException {
    private final Map<String, String> validationErrors;

    public ValidationException(String message, Map<String, String> validationErrors, Throwable cause, Long orderId, Integer httpCode) {
        super(message, cause, orderId, httpCode);
        this.validationErrors = validationErrors;
    }
}
