package ru.oskelly.concierge.exception;

import lombok.Getter;

/**
 * Базовый класс для исключений
 */
@Getter
public class ConciergeAbstractException extends RuntimeException {
    private final Integer httpCode;
    private final Long orderId;

    public ConciergeAbstractException(String message, Throwable cause, Long orderId, Integer httpCode) {
        super(message, cause);
        this.httpCode = httpCode;
        this.orderId = orderId;
    }
}
