package ru.oskelly.concierge.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.event.OrderAssignmentSourcerStartEvent;
import ru.oskelly.concierge.service.OrderAssignmentSourcerService;

@Component
@RequiredArgsConstructor
public class OrderAssignmentSourcerStartEventListener {
    private final OrderAssignmentSourcerService orderAssignmentSourcerService;

    /**
     * Слушатель события старта процесса назначения Сорсера
     */
    @EventListener
    public void onEvent(OrderAssignmentSourcerStartEvent event) {
        orderAssignmentSourcerService.requestAvailableSourcer(event.getOrderId());
    }
}
