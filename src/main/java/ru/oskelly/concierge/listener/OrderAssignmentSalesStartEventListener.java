package ru.oskelly.concierge.listener;

import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.event.OrderAssignmentSalesStartEvent;
import ru.oskelly.concierge.service.OrderAssignmentSalesService;

@Component
@RequiredArgsConstructor
public class OrderAssignmentSalesStartEventListener {
    private final OrderAssignmentSalesService orderAssignmentSalesService;

    /**
     * Слушатель события старта процесса назначения Сейлза
     */
    @EventListener
    public void onEvent(OrderAssignmentSalesStartEvent event) {
        orderAssignmentSalesService.requestAvailableSales(event.getOrderId());
    }
}
