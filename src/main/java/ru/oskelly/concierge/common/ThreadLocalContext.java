package ru.oskelly.concierge.common;


import java.util.HashMap;
import java.util.Map;

/**
 * Класс для хранения контекста в ThreadLocal
 */
public class ThreadLocalContext {
    private static final ThreadLocal<Map<ContextConstants, Object>> threadContext = ThreadLocal.withInitial(HashMap::new);

    public static void put(ContextConstants key, Object value) {
        threadContext.get().put(key, value);
    }

    public static <T> T get(ContextConstants key, Class<T> clazz) {
        return clazz.cast(threadContext.get().get(key));
    }

    public static void remove(ContextConstants key) {
        threadContext.get().remove(key);
    }

    public static void clean() {
        threadContext.remove();
    }
}
