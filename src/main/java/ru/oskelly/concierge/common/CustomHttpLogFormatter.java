package ru.oskelly.concierge.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Nonnull;
import org.springframework.web.util.UriComponentsBuilder;
import org.zalando.logbook.Correlation;
import org.zalando.logbook.HttpHeaders;
import org.zalando.logbook.HttpLogFormatter;
import org.zalando.logbook.HttpRequest;
import org.zalando.logbook.HttpResponse;
import org.zalando.logbook.Precorrelation;
import org.zalando.logbook.json.JsonHttpLogFormatter;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Класс CustomHttpLogFormatter реализует интерфейс HttpLogFormatter и предоставляет
 * методы для форматирования HTTP-запросов и ответов в читаемый вид для логирования.
 */
public final class CustomHttpLogFormatter implements HttpLogFormatter {
    private static final String EMPTY_ARRAY = "[]";
    private static final String EMPTY_STRING = "";

    private final ObjectMapper objectMapper;
    private final JsonHttpLogFormatter jsonHttpLogFormatter;

    /**
     * Конструктор класса CustomHttpLogFormatter.
     *
     * @param objectMapper Объект ObjectMapper для сериализации/десериализации JSON.
     */
    public CustomHttpLogFormatter(ObjectMapper objectMapper) {
        this.objectMapper = Objects.requireNonNull(objectMapper, "ObjectMapper must not be null");
        this.jsonHttpLogFormatter = new JsonHttpLogFormatter(objectMapper);
    }

    @Override
    public String format(@Nonnull Precorrelation precorrelation, @Nonnull HttpRequest request) throws IOException {
        Map<String, Object> content = jsonHttpLogFormatter.prepare(precorrelation, request);

        return String.format(
                """
                Входящий запрос ::
                >>>
                    Correlation: %s
                    Method:      %s
                    Path:        %s
                    Headers:     %s
                    Params:      %s
                    Body:        %s
                """,
                precorrelation.getId(),
                request.getMethod(),
                request.getPath(),
                formatHeaders(request.getHeaders()),
                formatParams(request.getRequestUri()),
                formatBody(content.get("body"))
        );
    }

    @Override
    public String format(@Nonnull Correlation correlation, @Nonnull HttpResponse response) throws IOException {
        Map<String, Object> content = jsonHttpLogFormatter.prepare(correlation, response);

        return String.format(
                """
                Исходящий ответ ::
                >>>
                    Correlation: %s
                    HttpStatus:  %s
                    Reason:      %s
                    Headers:     %s
                    Body:        %s
                    Duration:    %s
                """,
                correlation.getId(),
                response.getStatus(),
                response.getReasonPhrase(),
                formatHeaders(response.getHeaders()),
                formatBody(content.get("body")),
                formatDuration(content.get("duration"))
        );
    }

    /**
     * Форматирует заголовки HTTP-запроса/ответа в строку.
     */
    private String formatHeaders(HttpHeaders headers) {
        if (headers == null || headers.isEmpty()) {
            return EMPTY_ARRAY;
        }

        String headersContent = headers.entrySet().stream()
                .map(entry -> String.format("\t\t- %s: %s", entry.getKey(), entry.getValue()))
                .collect(Collectors.joining("\n"));

        return String.format("[\n%s\n\t]", headersContent);
    }

    /**
     * Форматирует тело HTTP-запроса/ответа в строку.
     */
    private String formatBody(Object body) throws IOException {
        return Objects.isNull(body) ? EMPTY_STRING : objectMapper.writeValueAsString(body);
    }

    /**
     * Форматирует параметры запроса в строку.
     */
    private String formatParams(String queryString) {
        if (queryString == null || !queryString.contains("?")) {
            return EMPTY_STRING;
        }

        Map<String, String> queryParams = UriComponentsBuilder
                .fromUriString(queryString)
                .build()
                .getQueryParams()
                .toSingleValueMap();

        if (queryParams.isEmpty()) {
            return EMPTY_STRING;
        }

        StringJoiner joiner = new StringJoiner("; ");
        queryParams.forEach((key, value) -> joiner.add(key + "=" + value));
        return joiner.toString();
    }

    /**
     * Форматирует длительность выполнения запроса/ответа в строку.
     */
    private String formatDuration(Object duration) {
        return String.format("%s ms", duration);
    }
}
