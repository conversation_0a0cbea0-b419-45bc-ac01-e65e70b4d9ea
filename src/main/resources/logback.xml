<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Проверяем, активен ли профиль "local" -->
    <springProfile name="!local">
        <appender name="json_console" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <includeCallerData>true</includeCallerData>
                <customFields>{"appname":"${APP_NAME}"}</customFields>
            </encoder>
        </appender>
        <logger name="org.apache.http" level="ERROR"/>
        <root level="INFO">
            <appender-ref ref="json_console"/>
        </root>
    </springProfile>

    <!-- Конфигурация для профиля "local" -->
    <springProfile name="local">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <logger name="org.apache.http" level="ERROR"/>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>
</configuration>
