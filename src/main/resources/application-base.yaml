spring:
  application:
    name: oskelly-concierge
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:postgresql://${DATABASE_HOST:localhost}:${DATABASE_PORT:5432}/${DATABASE_NAME:oskelly-concierge}
    username: ${DATABASE_USER:oskelly}
    password: ${DATABASE_PASSWORD:qwerty}
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    locations: classpath:db/migration

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    properties:
      security.protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_PLAINTEXT}
      sasl.mechanism: ${KAFKA_SASL_MECHANISM:SCRAM-SHA-512}
      sasl.jaas.config: ${KAFKA_SASL_JAAS_CONFIG:org.apache.kafka.common.security.scram.ScramLoginModule required username="${KAFKA_USER:oskelly}" password="${KAFKA_PASSWORD:oskelly123}";}
    consumer:
      username: ${KAFKA_USER:oskelly}
      password: ${KAFKA_PASSWORD:oskelly123}
      group-id: ${KAFKA_CONCIERGE_GROUP_ID:oskelly-concierge}
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.common.serialization.StringDeserializer
        spring.json.trusted.packages: ru.oskelly.concierge.kafka.dto
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        max.block.ms: 2000
        retry.backoff.ms: 500

server:
  port: ${PORT:8080}

springdoc:
  swagger-ui:
    config-url: ${SWAGGER_CONFIG_URL:/v3/api-docs/swagger-config}
    url: ${SWAGGER_URL:/v3/api-docs}
    disable-swagger-default-url: true

concierge:
  offer:
    max-count-product-platform: 5
    max-count-offer: 2

management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus

monolith:
  host: ${MONOLITH_HOSTNAME:https://oskelly.ru}

bitrix:
  url: ${BITRIX_URL:https://dev-bitrix24.oskelly.tech}
  webhook: ${BITRIX_WEBHOOK:w7z3svls1ovbiha3}
  user-id: ${BITRIX_USER_ID:28486}
  scheduler:
    shoppers:
      cron: ${BITRIX_SHOPPERS_FETCH_PERIODICITY:0 0/2 * * * ?}
#       cron: ${BITRIX_SHOPPERS_FETCH_PERIODICITY:0 0 */2 * * ?}

kafka:
  topics:
    # топик запросов на получение свободного сейлза
    sales-requests-available-topic:
      name: ${KAFKA_CONCIERGE_SALES_REQUESTS_AVAILABLE_TOPIC:sales-requests-available}
      enabled: ${KAFKA_CONCIERGE_SALES_REQUESTS_AVAILABLE_TOPIC_ENABLED:true}

    # топик доступных сейлзов для назначения на заявку
    sales-available-topic:
      name: ${KAFKA_CONCIERGE_SALES_AVAILABLE_TOPIC:sales-available}
      enabled: ${KAFKA_CONCIERGE_SALES_AVAILABLE_TOPIC_ENABLED:true}

    # топик событий на назначение клиента сотруднику
    sales-assignments-client-topic:
      name: ${KAFKA_CONCIERGE_SALES_ASSIGNMENTS_CLIENT_TOPIC:sales-assignments-client}
      enabled: ${KAFKA_CONCIERGE_SALES_ASSIGNMENTS_CLIENT_TOPIC_ENABLED:true}

    # топик событий на перераспределение заявок сейлза
    sales-requests-reassignment-orders-topic:
      name: ${KAFKA_CONCIERGE_SALES_REQUESTS_REASSIGNMENT_ORDERS_TOPIC:sales-requests-reassignment-orders}
      enabled: ${KAFKA_CONCIERGE_SALES_REQUESTS_REASSIGNMENT_ORDERS_TOPIC_ENABLED:true}

    # топик запросов на получение свободного сорсера
    sourcer-requests-available-topic:
      name: ${KAFKA_CONCIERGE_SOURCER_REQUESTS_AVAILABLE_TOPIC:sourcer-requests-available}
      enabled: ${KAFKA_CONCIERGE_SOURCER_REQUESTS_AVAILABLE_TOPIC_ENABLED:true}

    # топик доступных сорсеров для назначения на заявку
    sourcer-available-topic:
      name: ${KAFKA_CONCIERGE_SOURCER_AVAILABLE_TOPIC:sourcer-available}
      enabled: ${KAFKA_CONCIERGE_SOURCER_AVAILABLE_TOPIC_ENABLED:true}

    # топик событий на перераспределение заявок сорсера
    sourcer-requests-reassignment-orders-topic:
      name: ${KAFKA_CONCIERGE_SOURCER_REQUESTS_REASSIGNMENT_ORDERS_TOPIC:sourcer-requests-reassignment-orders}
      enabled: ${KAFKA_CONCIERGE_SOURCER_REQUESTS_REASSIGNMENT_ORDERS_TOPIC_ENABLED:true}

    # топик сообщений по предложениям для отправки клиенту в WhatsApp
    offer-message-client-send-request:
      name: ${KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_TOPIC:offer-message-client-send-request}
      enabled: ${KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_TOPIC_ENABLED:true}

    # топик результатов отправки сообщений по предложениям клиенту в WhatsApp
    offer-message-client-send-request-result:
      name: ${KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_RESULT_TOPIC:offer-message-client-send-request-result}
      enabled: ${KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_RESULT_TOPIC_ENABLED:true}