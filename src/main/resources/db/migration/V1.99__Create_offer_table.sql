CREATE SEQUENCE IF NOT EXISTS offer_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE offer (
    id BIGINT NOT NULL PRIMARY KEY DEFAULT nextval('offer_seq'),
    shipment_id BIGINT NOT NULL,
    seller_id BIGINT,
    type VARCHAR(50),
    price DECIMAL, -- Без указания точности для BigDecimal
    delivery_date TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    seller_type VARCHAR,
    CONSTRAINT fk_offer_shipment FOREIGN KEY (shipment_id) REFERENCES shipment(id)
);

CREATE INDEX idx_offer_shipment_id ON offer(shipment_id);
CREATE INDEX idx_offer_seller_id ON offer(seller_id);
CREATE INDEX idx_offer_valid_until ON offer(valid_until);

COMMENT ON COLUMN offer.id IS 'Идентификатор предложения.';
COMMENT ON COLUMN offer.shipment_id IS 'Идентификатор заказа, к которому относится предложение.';
COMMENT ON COLUMN offer.seller_id IS 'Идентификатор продавца, предложившего предложение.';
COMMENT ON COLUMN offer.type IS 'Тип предложения.';
COMMENT ON COLUMN offer.price IS 'Цена предложения.';
COMMENT ON COLUMN offer.delivery_date IS 'Дата доставки предложения.';
COMMENT ON COLUMN offer.valid_until IS 'Дата окончания действия предложения.';
COMMENT ON COLUMN offer.seller_type IS 'Тип продавца.';