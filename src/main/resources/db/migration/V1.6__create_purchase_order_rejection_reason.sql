-- Создание последовательности для генерации ID
CREATE SEQUENCE rejection_reason_id_seq START WITH 1 INCREMENT BY 1;

-- Создание таблицы rejection_reason
CREATE TABLE rejection_reason (
                                  id BIGINT PRIMARY KEY DEFAULT nextval('rejection_reason_id_seq'),
                                  object_type VARCHAR(50) NOT NULL,
                                  order_status VARCHAR(50),
                                  reason_text TEXT NOT NULL,
                                  requires_description BOOLEAN NOT NULL
);

-- Добавление ограничения для проверки, что order_status может быть NULL только если object_type не ORDER
ALTER TABLE rejection_reason
    ADD CONSTRAINT chk_order_status CHECK (
        (object_type = 'PURCHASE_ORDER' AND order_status IS NOT NULL) OR
        (object_type != 'PURCHASE_ORDER' AND order_status IS NULL)
        );

-- Создание индекса на поле object_type для ускорения поиска
CREATE INDEX idx_rejection_reason_object_type ON rejection_reason (object_type);

-- Создание индекса на поле order_status для ускорения поиска
CREATE INDEX idx_rejection_reason_order_status ON rejection_reason (order_status);

CREATE TABLE purchase_order_rejection_reason (
                                                 purchase_order_id BIGINT NOT NULL,
                                                 rejection_reason_id BIGINT NOT NULL,
                                                 PRIMARY KEY (purchase_order_id, rejection_reason_id),
                                                 CONSTRAINT fk_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES purchase_order(id),
                                                 CONSTRAINT fk_rejection_reason FOREIGN KEY (rejection_reason_id) REFERENCES rejection_reason(id)
);