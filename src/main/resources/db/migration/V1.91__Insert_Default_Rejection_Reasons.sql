-- Add rejection reasons if they don't exist
INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'IN_PROGRESS_SALES', 'Спам', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Спам'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'IN_PROGRESS_SALES'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'IN_PROGRESS_SALES', 'Нецелевое обращение', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Нецелевое обращение'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'IN_PROGRESS_SALES'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'IN_PROGRESS_SALES', 'Дубль', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Дубль'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'IN_PROGRESS_SALES'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'IN_PROGRESS_SALES', 'Не можем выполнить', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Не можем выполнить'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'IN_PROGRESS_SALES'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'IN_PROGRESS_SALES', 'Другое', true
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Другое'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'IN_PROGRESS_SALES'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'NEW', 'Спам', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Спам'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'NEW'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'NEW', 'Нецелевое обращение', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Нецелевое обращение'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'NEW'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'NEW', 'Дубль', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Дубль'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'NEW'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'NEW', 'Не можем выполнить', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Не можем выполнить'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'NEW'
);

INSERT INTO rejection_reason (object_type, order_status, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'NEW', 'Другое', true
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Другое'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'NEW'
);