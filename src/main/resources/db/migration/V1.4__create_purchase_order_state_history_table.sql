-- Создание последовательности для генерации ID
CREATE SEQUENCE purchase_order_state_history_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- Создание таблицы purchase_order_state_history
CREATE TABLE purchase_order_state_history (
                                              id BIGINT PRIMARY KEY DEFAULT nextval('purchase_order_state_history_seq'),
                                              purchase_order_id BIGINT NOT NULL,
                                              user_id BIGINT,
                                              source_state VARCHAR(50),
                                              target_state VARCHAR(50) NOT NULL,
                                              transition_date TIMESTAMP WITH TIME ZONE NOT NULL,
                                              comment TEXT
);

-- Комментарии к таблице и её колонкам
COMMENT ON TABLE purchase_order_state_history IS 'Таблица для хранения истории изменений состояний заказов.';

COMMENT ON COLUMN purchase_order_state_history.id IS 'Уникальный идентификатор записи в таблице.';
COMMENT ON COLUMN purchase_order_state_history.purchase_order_id IS 'Идентификатор заказа, к которому относится история изменений.';
COMMENT ON COLUMN purchase_order_state_history.user_id IS 'Идентификатор пользователя, выполнившего переход между состояниями.';
COMMENT ON COLUMN purchase_order_state_history.source_state IS 'Исходное состояние заказа перед переходом.';
COMMENT ON COLUMN purchase_order_state_history.target_state IS 'Целевое состояние заказа после перехода.';
COMMENT ON COLUMN purchase_order_state_history.transition_date IS 'Дата и время перехода между состояниями.';
COMMENT ON COLUMN purchase_order_state_history.comment IS 'Комментарий, описывающий причину или детали перехода.';

-- Опционально: Добавление индексов для ускорения поиска
CREATE INDEX idx_purchase_order_id ON purchase_order_state_history (purchase_order_id);
CREATE INDEX idx_transition_date ON purchase_order_state_history (transition_date);