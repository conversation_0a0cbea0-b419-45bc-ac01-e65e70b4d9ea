-- === Для object_type = 'PURCHASE_ORDER' (order_status разрешён) ===

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Заказчик отменил заявку', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Заказчик отменил заявку'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Спам', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Спам'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Нецелевое обращение', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Нецелевое обращение'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Неверные контактные данные', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Неверные контактные данные'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Не смогли выполнить заявку', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Не смогли выполнить заявку'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Дубль', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Дубль'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Шопер отказался от договоренностей', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Шопер отказался от договоренностей'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PURCHASE_ORDER', 'ALL', 'SALES', 'Другое', true
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Другое'
      AND object_type = 'PURCHASE_ORDER'
      AND order_status = 'ALL'
      AND role = 'SALES'
);


-- === Для object_type = 'PRODUCT' (order_status должен быть NULL) ===

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PRODUCT', NULL, 'SOURCER', 'Не смогли найти товар', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Не смогли найти товар'
      AND object_type = 'PRODUCT'
      AND order_status IS NULL
      AND role = 'SOURCER'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PRODUCT', NULL, 'SOURCER', 'Другое', true
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Другое'
      AND object_type = 'PRODUCT'
      AND order_status IS NULL
      AND role = 'SOURCER'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PRODUCT', NULL, 'SALES', 'Не смогли найти товар', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Не смогли найти товар'
      AND object_type = 'PRODUCT'
      AND order_status IS NULL
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PRODUCT', NULL, 'SALES', 'Заказчик отменил товар', false
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Заказчик отменил товар'
      AND object_type = 'PRODUCT'
      AND order_status IS NULL
      AND role = 'SALES'
);

INSERT INTO rejection_reason (object_type, order_status, role, reason_text, requires_description)
SELECT 'PRODUCT', NULL, 'SALES', 'Другое', true
WHERE NOT EXISTS (
    SELECT 1 FROM rejection_reason
    WHERE reason_text = 'Другое'
      AND object_type = 'PRODUCT'
      AND order_status IS NULL
      AND role = 'SALES'
);