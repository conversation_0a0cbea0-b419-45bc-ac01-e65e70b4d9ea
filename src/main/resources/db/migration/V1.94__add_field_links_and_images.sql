ALTER TABLE public.shipment
    ADD COLUMN IF NOT EXISTS created_at timestamp with time zone,
    ADD COLUMN IF NOT EXISTS model_id bigint,
    ADD COLUMN IF NOT EXISTS shipment_size jsonb,
    ADD COLUMN IF NOT EXISTS description text,
    ADD COLUMN IF NOT EXISTS images jsonb,
    ADD COLUMN IF NOT EXISTS links jsonb;

COMMENT ON TABLE public.shipment IS 'Товары для заявок';

COMMENT ON COLUMN public.shipment.id IS 'Идентификатор';
COMMENT ON COLUMN public.shipment.purchase_order_id IS 'Идентификатор заказа';
COMMENT ON COLUMN public.shipment.category_id IS 'Идентификатор категории';
COMMENT ON COLUMN public.shipment.brand_id IS 'Идентификатор бренда';
