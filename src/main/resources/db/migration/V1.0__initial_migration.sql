CREATE SEQUENCE IF NOT EXISTS comment_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS purchase_order_seq START WITH 100 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS shipment_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE comment
(
    id                BIGINT NOT NULL,
    purchase_order_id BIGINT,
    author_id         BIGINT,
    is_pined          BOOLEAN,
    text              TEXT,
    creation_date     TIMESTAMP WITHOUT TIME ZONE,
    update_date       TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_comment PRIMARY KEY (id)
);

CREATE TABLE purchase_order
(
    id            BIGINT       NOT NULL,
    user_id       BIGINT       NOT NULL,
    source        VARCHAR(255),
    description   TEXT,
    creation_date TIMESTAMP WITHOUT TIME ZONE,
    change_date   TIMESTAMP WITHOUT TIME ZONE,
    status        VARCHAR(255) NOT NULL,
    sales_id      BIGINT,
    sourcer_id    BIGINT,
    images        JSON<PERSON>,
    orders        JSO<PERSON><PERSON>,
    CONSTRAINT pk_purchase_order PRIMARY KEY (id)
);

CREATE TABLE shipment
(
    id                   BIGINT NOT NULL,
    purchase_order_id    BIGINT NOT NULL,
    category_id          BIGINT,
    brand_id             BIGINT,
    material_atribute_id BIGINT,
    color_atribute_id    BIGINT,
    CONSTRAINT pk_shipment PRIMARY KEY (id)
);

ALTER TABLE comment
    ADD CONSTRAINT FK_COMMENT_ON_PURCHASE_ORDER FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id);

ALTER TABLE shipment
    ADD CONSTRAINT FK_SHIPMENT_ON_PURCHASE_ORDER FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id);