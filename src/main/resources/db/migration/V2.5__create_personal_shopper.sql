CREATE SEQUENCE IF NOT EXISTS personal_shopper_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE IF NOT EXISTS personal_shopper (
    id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    nickname VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    interaction_type VARCHAR(50) NOT NULL,
    payment_format VARCHAR(50) NOT NULL,
    priority BOOLEAN NOT NULL,
    categories jsonb,
    brands jsonb,
    access_source VARCHAR(50)
);

CREATE UNIQUE INDEX idx_personal_shopper_user_id ON personal_shopper(user_id);

-- Добавляем комментарии к полям (на русском)
COMMENT ON TABLE personal_shopper IS 'Сущность PersonalShopper — данные по персональному шопперу';

COMMENT ON COLUMN personal_shopper.id IS 'Уникальный идентификатор записи';
COMMENT ON COLUMN personal_shopper.user_id IS 'Идентификатор пользователя в системе';
COMMENT ON COLUMN personal_shopper.nickname IS 'Никнейм персонального шоппера';
COMMENT ON COLUMN personal_shopper.email IS 'Электронная почта персонального шоппера';
COMMENT ON COLUMN personal_shopper.name IS 'Полное имя персонального шоппера';
COMMENT ON COLUMN personal_shopper.interaction_type IS 'Тип взаимодействия: Интернет сайты, Мультибренд, Под запрос, Универсал';
COMMENT ON COLUMN personal_shopper.payment_format IS 'Формат оплаты: Предоплата, Постоплата';
COMMENT ON COLUMN personal_shopper.priority IS 'Признак приоритетности шоппера (true - Да, false - Нет)';
COMMENT ON COLUMN personal_shopper.categories IS 'Список категорий';
COMMENT ON COLUMN personal_shopper.brands IS 'Список брендов';
COMMENT ON COLUMN personal_shopper.access_source IS 'Источник доступа: Интернет сайты, Мультибренд, Под запрос, Универсал';