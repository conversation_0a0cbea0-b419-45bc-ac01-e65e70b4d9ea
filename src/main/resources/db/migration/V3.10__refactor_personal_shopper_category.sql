DROP TABLE IF EXISTS personal_shopper_category;
DROP TABLE IF EXISTS categories;
DROP SEQUENCE IF EXISTS category_seq;

-- Таблица категорий байера
CREATE TABLE IF NOT EXISTS shopper_category (
    id   BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    code VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL
);

COMMENT ON TABLE public.shopper_category IS 'Категории шопперов (байеров)';
COMMENT ON COLUMN shopper_category.id IS 'Уникальный идентификатор записи в таблице.';
COMMENT ON COLUMN shopper_category.code IS 'Код категории шоппера.';
COMMENT ON COLUMN shopper_category.name IS 'Название категории шоппера.';

-- Вставка категорий байера
INSERT INTO shopper_category (code, name) VALUES
    ('CLOTHING', 'Одежда'),
    ('FOOTWEAR', 'Обувь'),
    ('BAGS_ACCESSORIES', 'Сумки и аксессуары'),
    ('WATCHES_JEWELRY', 'Часы и ювелирные украшения'),
    ('RARE_LIMITED', 'Редкие и лимитированные вещи')
ON CONFLICT (code) DO NOTHING;

-- Связующая таблица для связи многие-ко-многим между personal_shopper и categories
CREATE TABLE IF NOT EXISTS personal_shopper_category (
    personal_shopper_id BIGINT NOT NULL,
    shopper_category_id BIGINT NOT NULL,
    PRIMARY KEY (personal_shopper_id, shopper_category_id),
    CONSTRAINT fk_personal_shopper_category_ps
        FOREIGN KEY (personal_shopper_id) REFERENCES personal_shopper(id),
    CONSTRAINT fk_personal_shopper_category_cat
        FOREIGN KEY (shopper_category_id) REFERENCES shopper_category(id)
);