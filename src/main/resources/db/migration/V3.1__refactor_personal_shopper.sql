DROP TABLE personal_shopper;
DROP SEQUENCE personal_shopper_seq;

-- Последовательности для всех таблиц
CREATE SEQUENCE personal_shopper_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE brand_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE category_seq START WITH 1 INCREMENT BY 1;

-- Таблица категорий с собственной последовательностью
CREATE TABLE categories (
    id BIGINT PRIMARY KEY DEFAULT nextval('category_seq'),
    category_id BIGINT NOT NULL
);

-- Таблица брендов
CREATE TABLE brands (
    id BIGINT PRIMARY KEY DEFAULT nextval('brand_seq'),
    brand_id BIGINT NOT NULL
);

-- О<PERSON>новная таблица personal_shopper
CREATE TABLE personal_shopper (
    id BIGINT PRIMARY KEY DEFAULT nextval('personal_shopper_seq'),
    user_id BIGINT NOT NULL,
    nickname VA<PERSON>HA<PERSON>(255),
    email VARCHAR(255),
    name VARCHAR(255),
    interaction_type VARCHAR(50) NOT NULL,
    payment_format VARCHAR(50) NOT NULL,
    priority BOOLEAN NOT NULL,
    access_source VARCHAR(50),
    date_shopper_status TIMESTAMP WITH TIME ZONE
);

-- Связующая таблица для связи многие-ко-многим между personal_shopper и categories
CREATE TABLE personal_shopper_category (
    personal_shopper_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    PRIMARY KEY (personal_shopper_id, category_id),
    CONSTRAINT fk_personal_shopper_category_ps
        FOREIGN KEY (personal_shopper_id) REFERENCES personal_shopper(id),
    CONSTRAINT fk_personal_shopper_category_cat
        FOREIGN KEY (category_id) REFERENCES categories(id)
);

-- Связующая таблица для связи многие-ко-многим между personal_shopper и brands
CREATE TABLE personal_shopper_brand (
    personal_shopper_id BIGINT NOT NULL,
    brand_id BIGINT NOT NULL,
    PRIMARY KEY (personal_shopper_id, brand_id),
    CONSTRAINT fk_personal_shopper_brand_ps
        FOREIGN KEY (personal_shopper_id) REFERENCES personal_shopper(id),
    CONSTRAINT fk_personal_shopper_brand_brand
        FOREIGN KEY (brand_id) REFERENCES brands(id)
);