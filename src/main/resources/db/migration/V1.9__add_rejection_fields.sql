-- Добавляем недостающие колонки и корректируем типы данных
ALTER TABLE purchase_order
    ADD COLUMN rejection_reason TEXT,
    ADD COLUMN rejection_description TEXT;

-- Изменяем тип колонок с timestamp на timestamp with time zone для хранения временных зон
ALTER TABLE purchase_order
    ALTER COLUMN creation_date TYPE TIMESTAMP WITH TIME ZONE,
    ALTER COLUMN change_date TYPE TIMESTAMP WITH TIME ZONE;

-- Комментарии к колонкам для документации
COMMENT ON COLUMN purchase_order.rejection_reason IS 'Причина отказа в заказе';
COMMENT ON COLUMN purchase_order.rejection_description IS 'Описание причины отказа в заказе';
COMMENT ON COLUMN purchase_order.creation_date IS 'Дата создания заказа с учетом временной зоны';
COMMENT ON COLUMN purchase_order.change_date IS 'Дата изменения заказа с учетом временной зоны';