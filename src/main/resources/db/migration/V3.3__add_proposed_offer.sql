CREATE SEQUENCE proposed_offer_seq START 1 INCREMENT 1;

CREATE TABLE prosposed_offer (
    id BIGINT PRIMARY KEY NOT NULL,
    ruble_price DECIMAL,
    delivery_date TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    creation_date TIMESTAMP WITH TIME ZONE,
    currency VARCHAR(255),
    currency_price DECIMAL,
    has_receipt BOOLEAN,
    is_complete_set BOOLEAN,
    has_custom_commission BOOLEAN,
    commission DECIMAL,
    comment TEXT,
    offer_id BIGINT REFERENCES offer(id) ON DELETE CASCADE
);

COMMENT ON TABLE prosposed_offer IS 'Таблица предложенных предложений';
COMMENT ON COLUMN prosposed_offer.id IS 'Идентификатор предложения';
COMMENT ON COLUMN prosposed_offer.ruble_price IS 'Цена в рублях';
COMMENT ON COLUMN prosposed_offer.delivery_date IS 'Дата доставки';
COMMENT ON COLUMN prosposed_offer.valid_until IS 'Срок действия предложения';
COMMENT ON COLUMN prosposed_offer.creation_date IS 'Дата создания предложения';
COMMENT ON COLUMN prosposed_offer.currency IS 'Валюта предложения';
COMMENT ON COLUMN prosposed_offer.currency_price IS 'Цена в валюте';
COMMENT ON COLUMN prosposed_offer.has_receipt IS 'Наличие чека';
COMMENT ON COLUMN prosposed_offer.is_complete_set IS 'Полная комплектация';
COMMENT ON COLUMN prosposed_offer.has_custom_commission IS 'Наличие на собственной комиссии';
COMMENT ON COLUMN prosposed_offer.commission IS 'Комиссия';
COMMENT ON COLUMN prosposed_offer.comment IS 'Комментарий';
COMMENT ON COLUMN prosposed_offer.offer_id IS 'Идентификатор предложения';


-- Drop columns that have been moved to proposed_offer
ALTER TABLE offer DROP COLUMN delivery_date;
ALTER TABLE offer DROP COLUMN valid_until;
ALTER TABLE offer DROP COLUMN price;