package ru.oskelly.concierge.service.component;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.repository.ShipmentRepository;
import ru.oskelly.concierge.exception.ShipmentNotFoundException;
import ru.oskelly.concierge.service.dto.BestOfferDTO;
import ru.oskelly.concierge.service.dto.ComparisonCriterion;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DefaultBestOfferComponentTest {

    @Mock
    private ShipmentRepository shipmentRepository;

    @InjectMocks
    private DefaultBestOfferComponent component;

    private Shipment testShipment;
    private Offer testOffer;
    private ProposedOffer testProposedOffer;

    @BeforeEach
    void setUp() {
        testShipment = new Shipment();
        testShipment.setId(1L);

        testOffer = new Offer();
        testOffer.setId(1L);

        testProposedOffer = new ProposedOffer();
        testProposedOffer.setId(1L);
        testProposedOffer.setRublePrice(BigDecimal.valueOf(1500));
        testProposedOffer.setDeliveryDate(ZonedDateTime.now());
    }

    @Test
    void testFindBestOffersWithEmptyOffers() {
        testShipment.setOffers(Collections.emptySet());
        List<BestOfferDTO> result = component.findBestOffers(testShipment);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindBestOffersWithMultipleCriteria() {
        Set<Offer> offers = new HashSet<>();
        offers.add(testOffer);
        testShipment.setOffers(offers);
        testOffer.setProposedOffers(Collections.singleton(testProposedOffer));

        List<BestOfferDTO> result = component.findBestOffers(testShipment);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getCriteria().containsAll(
            Set.of(ComparisonCriterion.DELIVERY_DATE,
                   ComparisonCriterion.PRICE)));
    }

    @Test
    void testFindBestOffersByIdWithExistingShipment() throws ShipmentNotFoundException {
        when(shipmentRepository.findById(anyLong())).thenReturn(java.util.Optional.of(testShipment));

        List<BestOfferDTO> result = component.findBestOffers(1L);
        assertNotNull(result);
        verify(shipmentRepository).findById(1L);
    }

    @Test
    void testFindBestOffersByIdWithNonExistingShipment() {
        when(shipmentRepository.findById(anyLong())).thenReturn(java.util.Optional.empty());

        assertThrows(ShipmentNotFoundException.class, () -> component.findBestOffers(1L));
        verify(shipmentRepository).findById(1L);
    }
}
