package ru.oskelly.concierge.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import ru.oskelly.concierge.controller.dto.OrderInfoDTO;
import ru.oskelly.concierge.service.component.WorkerOrdes;

import java.util.Set;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class DefaultPurchaseOrderServiceTest {

    @InjectMocks
    private DefaultPurchaseOrderService purchaseOrderService;

    @Mock
    private WorkerOrdes workerOrdes;

    @Test
    void processOrderInfo_callsWorkerOrdesProcessOrderInfo() {
        // Given
        Long productId = 123L;
        Long orderId = 456L;
        Long clientId = 789L;
        OrderInfoDTO orderInfo = new OrderInfoDTO(Set.of(productId), orderId, clientId);

        // When
        purchaseOrderService.processOrderInfo(orderInfo);

        // Then
        verify(workerOrdes).processOrderInfo(orderInfo);
    }
}