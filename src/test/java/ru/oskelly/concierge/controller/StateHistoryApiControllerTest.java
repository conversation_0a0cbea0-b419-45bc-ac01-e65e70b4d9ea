package ru.oskelly.concierge.controller;

import com.diffblue.cover.annotations.MethodsUnderTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;
import ru.oskelly.concierge.service.StateTransitionHistoryService;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {StateHistoryApiController.class})
@ExtendWith(SpringExtension.class)
@DisabledInAotMode
class StateHistoryApiControllerTest {
    @Autowired
    private StateHistoryApiController stateHistoryApiController;

    @MockitoBean
    private StateTransitionHistoryService stateTransitionHistoryService;

    @Test
    @DisplayName("Test getStateTransitionHistory(Long)")
    @MethodsUnderTest({"ResponseEntity StateHistoryApiController.getStateTransitionHistory(Long)"})
    void testGetStateTransitionHistory() {
        // Arrange
        when(stateTransitionHistoryService.getTransitionHistory(Mockito.<Long>any())).thenReturn(new ArrayList<>());

        // Act
        ResponseEntity<List<PurchaseOrderStateHistoryDTO>> actualStateTransitionHistory = stateHistoryApiController
                .getStateTransitionHistory(1L);

        // Assert
        verify(stateTransitionHistoryService).getTransitionHistory(eq(1L));
        HttpStatusCode statusCode = actualStateTransitionHistory.getStatusCode();
        assertInstanceOf(HttpStatus.class, statusCode);
        assertEquals(200, actualStateTransitionHistory.getStatusCodeValue());
        assertEquals(HttpStatus.OK, statusCode);
        assertTrue(actualStateTransitionHistory.getBody().isEmpty());
        assertTrue(actualStateTransitionHistory.hasBody());
        assertTrue(actualStateTransitionHistory.getHeaders().isEmpty());
    }
}
