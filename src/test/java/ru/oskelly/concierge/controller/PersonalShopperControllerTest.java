package ru.oskelly.concierge.controller;

import com.diffblue.cover.annotations.MethodsUnderTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.aot.DisabledInAotMode;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperCreateRequest;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.enums.AccessSource;
import ru.oskelly.concierge.data.model.enums.InteractionType;
import ru.oskelly.concierge.data.model.enums.PaymentFormat;
import ru.oskelly.concierge.data.model.enums.ShopperCategoryEnum;
import ru.oskelly.concierge.service.PersonalShopperService;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isA;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ContextConfiguration(classes = {PersonalShopperController.class})
@DisabledInAotMode
@ExtendWith(SpringExtension.class)
class PersonalShopperControllerTest {
    @Autowired
    private PersonalShopperController personalShopperController;

    @MockitoBean
    private PersonalShopperService personalShopperService;

    /**
     * Test {@link PersonalShopperController#createPersonalShopper(PersonalShopperCreateRequest)}.
     * <p>
     * Method under test: {@link PersonalShopperController#createPersonalShopper(PersonalShopperCreateRequest)}
     */
    @Test
    @DisplayName("Test createPersonalShopper(PersonalShopperCreateRequest)")
    @MethodsUnderTest({"ResponseEntity PersonalShopperController.createPersonalShopper(PersonalShopperCreateRequest)"})
    void testCreatePersonalShopper() {
        // Arrange
        PersonalShopper personalShopper = new PersonalShopper();
        personalShopper.setAccessSource(AccessSource.PERSONAL_BOUTIQUE_ACCESS);
        personalShopper.setBrands(new HashSet<>());
        personalShopper.setCategories(new HashSet<>());
        personalShopper.setDateShopperStatus(LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC));
        personalShopper.setEmail("<EMAIL>");
        personalShopper.setId(1L);
        personalShopper.setInteractionType(Set.of(InteractionType.INTERNET_SITES));
        personalShopper.setName("Name");
        personalShopper.setNickname("Nickname");
        personalShopper.setPaymentFormat(PaymentFormat.PREPAYMENT);
        personalShopper.setPriority(true);
        personalShopper.setUserId(1L);
        when(personalShopperService.createPersonalShopper(Mockito.<PersonalShopperCreateRequest>any()))
                .thenReturn(personalShopper);
        HashSet<ShopperCategoryEnum> categories = new HashSet<>();
        HashSet<Long> brands = new HashSet<>();

        // Act
        ResponseEntity<PersonalShopper> actualCreatePersonalShopperResult = personalShopperController.createPersonalShopper(
                new PersonalShopperCreateRequest(1L, "Nickname", "<EMAIL>", "Name", Set.of(InteractionType.INTERNET_SITES),
                        PaymentFormat.PREPAYMENT, categories, brands, AccessSource.PERSONAL_BOUTIQUE_ACCESS, true,
                        LocalDate.of(1970, 1, 1).atStartOfDay().atZone(ZoneOffset.UTC)));

        // Assert
        verify(personalShopperService).createPersonalShopper(isA(PersonalShopperCreateRequest.class));
        HttpStatusCode statusCode = actualCreatePersonalShopperResult.getStatusCode();
        assertInstanceOf(HttpStatus.class, statusCode);
        assertEquals(HttpStatus.OK, statusCode);
        assertTrue(actualCreatePersonalShopperResult.hasBody());
        assertTrue(actualCreatePersonalShopperResult.getHeaders().isEmpty());
        assertSame(personalShopper, actualCreatePersonalShopperResult.getBody());
    }

    /**
     * Test {@link PersonalShopperController#filterPersonalShopper()}.
     * <p>
     * Method under test: {@link PersonalShopperController#filterPersonalShopper()}
     */
    @Test
    @DisplayName("Test filterPersonalShopper()")
    @MethodsUnderTest({"ResponseEntity PersonalShopperController.filterPersonalShopper()"})
    void testFilterPersonalShopper() {
        // Arrange
        List<DescriptionStructureEnum> accessSources = new ArrayList<>();
        List<DescriptionStructureEnum> categories = new ArrayList<>();
        PersonalShopperFilter personalShopperFilter = new PersonalShopperFilter(accessSources, categories,
                new HashSet<>(), 0, 20);

        when(personalShopperService.filterPersonalShopper()).thenReturn(personalShopperFilter);

        // Act
        ResponseEntity<PersonalShopperFilter> actualFilterPersonalShopperResult = personalShopperController
                .filterPersonalShopper();

        // Assert
        verify(personalShopperService).filterPersonalShopper();
        HttpStatusCode statusCode = actualFilterPersonalShopperResult.getStatusCode();
        assertInstanceOf(HttpStatus.class, statusCode);
        assertEquals(HttpStatus.OK, statusCode);
        assertTrue(actualFilterPersonalShopperResult.hasBody());
        assertTrue(actualFilterPersonalShopperResult.getHeaders().isEmpty());
        assertSame(personalShopperFilter, actualFilterPersonalShopperResult.getBody());
    }

    /**
     * Test {@link PersonalShopperController#getPersonalShoppers(PersonalShopperFilter, Long, String)}.
     * <p>
     * Method under test: {@link PersonalShopperController#getPersonalShoppers(PersonalShopperFilter, Long, String)}
     */
    @Test
    @DisplayName("Test getPersonalShoppers(PersonalShopperFilter, Long, String)")
    @MethodsUnderTest({
            "ResponseEntity PersonalShopperController.getPersonalShoppers(PersonalShopperFilter, Long, String)"})
    void testGetPersonalShoppers() {
        // Arrange
        PaginatedShoppersResult paginatedShoppersResult = new PaginatedShoppersResult(new ArrayList<>(), 3L, 3L, 3L);

        when(personalShopperService.getPersonalShoppers(Mockito.<PersonalShopperFilter>any(), Mockito.<String>any()))
                .thenReturn(paginatedShoppersResult);
        List<DescriptionStructureEnum> accessSources = new ArrayList<>();
        List<DescriptionStructureEnum> categories = new ArrayList<>();

        // Act
        ResponseEntity<PaginatedShoppersResult> actualPersonalShoppers = personalShopperController
                .getPersonalShoppers(new PersonalShopperFilter(accessSources, categories, new HashSet<>(), 0, 20), 1L, "Search Text");

        // Assert
        verify(personalShopperService).getPersonalShoppers(isA(PersonalShopperFilter.class), eq("Search Text"));
        HttpStatusCode statusCode = actualPersonalShoppers.getStatusCode();
        assertInstanceOf(HttpStatus.class, statusCode);
        assertEquals(HttpStatus.OK, statusCode);
        assertTrue(actualPersonalShoppers.hasBody());
        assertTrue(actualPersonalShoppers.getHeaders().isEmpty());
        assertSame(paginatedShoppersResult, actualPersonalShoppers.getBody());
    }
}
