version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: oskelly-postgres
    environment:
      POSTGRES_DB: oskelly-concierge
      POSTGRES_USER: oskelly
      POSTGRES_PASSWORD: qwerty
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - oskelly-network

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: oskelly-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - oskelly-network

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: oskelly-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      # SASL Configuration
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,SASL_PLAINTEXT:SASL_PLAINTEXT
      KAFKA_SASL_ENABLED_MECHANISMS: SCRAM-SHA-512
      KAFKA_SASL_MECHANISM_INTER_BROKER_PROTOCOL: SCRAM-SHA-512
    networks:
      - oskelly-network

  # Oskelly Concierge Application
  oskelly-concierge:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: oskelly-concierge-app
    depends_on:
      - postgres
      - kafka
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: base
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: oskelly-concierge
      DATABASE_USER: oskelly
      DATABASE_PASSWORD: qwerty
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      KAFKA_SECURITY_PROTOCOL: PLAINTEXT
      KAFKA_SASL_MECHANISM: SCRAM-SHA-512
      KAFKA_USER: oskelly
      KAFKA_PASSWORD: oskelly123
      PORT: 8080
      # Bitrix configuration (можно настроить через переменные окружения)
      BITRIX_URL: https://dev-bitrix24.oskelly.tech
      BITRIX_WEBHOOK: w7z3svls1ovbiha3
      BITRIX_USER_ID: 28486
    networks:
      - oskelly-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  oskelly-network:
    driver: bridge