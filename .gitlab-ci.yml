stages:
  - test
  - build
  - deploy-stage
  - deploy-production

include:
  - project: 'oskelly.ru/devops/oskelly-gitlab-devops'
    ref: master
    file: 'app_templates/oskelly-concierge.yaml'

tests:
  stage: test
  image: maven:3.9.9-eclipse-temurin-21
  variables:
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  script:
    - mvn $MAVEN_CLI_OPTS test
  artifacts:
    reports:
      junit:
        - target/surefire-reports/TEST-*.xml
  cache:
    key: m2-cache
    paths:
      - $CI_PROJECT_DIR/.m2/*
    policy: pull-push
  only:
    - /^stage-[0-9]+\.[0-9]+\.[0-9]+$/
    - /^production-[0-9]+\.[0-9]+\.[0-9]+$/