<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">

    <mirrors>
        <mirror>
            <id>gitlab-maven</id>
            <name>GitLab Maven Proxy</name>
            <url>https://gitlab.oskelly.ru/api/v4/groups/14/-/packages/maven</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <mirror>
            <id>gitlab-maven</id>
            <name>GitLab Maven Proxy</name>
            <url>https://gitlab.oskelly.ru/api/v4/groups/14/-/packages/maven</url>
            <mirrorOf>central-google-maven</mirrorOf>
        </mirror>
        <mirror>
            <id>gitlab-maven</id>
            <name>GitLab Maven Proxy</name>
            <url>https://gitlab.oskelly.ru/api/v4/groups/14/-/packages/maven</url>
            <mirrorOf>central-aliyun</mirrorOf>
        </mirror>
    </mirrors>

    <servers>
        <server>
            <id>gitlab-maven</id>
            <configuration>
                <httpHeaders>
                    <property>
                        <name>Private-Token</name>
                        <value>${env.CI_JOB_TOKEN}</value>
                    </property>
                </httpHeaders>
            </configuration>
        </server>
    </servers>
</settings>
