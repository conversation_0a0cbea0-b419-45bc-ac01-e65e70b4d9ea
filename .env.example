# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=oskelly-concierge
DATABASE_USER=oskelly
DATABASE_PASSWORD=qwerty

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
<PERSON>AFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
KAFKA_SASL_MECHANISM=SCRAM-SHA-512
KAFKA_USER=oskelly
KAFKA_PASSWORD=oskelly123

# Kafka Topics Configuration
KAFKA_CONCIERGE_GROUP_ID=oskelly-concierge
KAFKA_CONCIERGE_SALES_REQUESTS_AVAILABLE_TOPIC=sales-requests-available
KAFKA_CONCIERGE_SALES_REQUESTS_AVAILABLE_TOPIC_ENABLED=true
KAFKA_CONCIERGE_SALES_AVAILABLE_TOPIC=sales-available
KAFKA_CONCIERGE_SALES_AVAILABLE_TOPIC_ENABLED=true
KAFKA_CONCIERGE_SALES_ASSIGNMENTS_CLIENT_TOPIC=sales-assignments-client
KAFKA_CONCIERGE_SALES_ASSIGNMENTS_CLIENT_TOPIC_ENABLED=true
KAFKA_CONCIERGE_SALES_REQUESTS_REASSIGNMENT_ORDERS_TOPIC=sales-requests-reassignment-orders
KAFKA_CONCIERGE_SALES_REQUESTS_REASSIGNMENT_ORDERS_TOPIC_ENABLED=true
KAFKA_CONCIERGE_SOURCER_REQUESTS_AVAILABLE_TOPIC=sourcer-requests-available
KAFKA_CONCIERGE_SOURCER_REQUESTS_AVAILABLE_TOPIC_ENABLED=true
KAFKA_CONCIERGE_SOURCER_AVAILABLE_TOPIC=sourcer-available
KAFKA_CONCIERGE_SOURCER_AVAILABLE_TOPIC_ENABLED=true
KAFKA_CONCIERGE_SOURCER_REQUESTS_REASSIGNMENT_ORDERS_TOPIC=sourcer-requests-reassignment-orders
KAFKA_CONCIERGE_SOURCER_REQUESTS_REASSIGNMENT_ORDERS_TOPIC_ENABLED=true

# Bitrix24 Configuration
BITRIX_URL=https://dev-bitrix24.oskelly.tech
BITRIX_WEBHOOK=w7z3svls1ovbiha3
BITRIX_USER_ID=28486

# Application Configuration
PORT=8080
SPRING_PROFILES_ACTIVE=base

# Swagger Configuration
SWAGGER_CONFIG_URL=/v3/api-docs/swagger-config
SWAGGER_URL=/v3/api-docs