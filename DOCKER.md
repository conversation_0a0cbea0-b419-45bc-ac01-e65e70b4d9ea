# Docker Setup для Oskelly Concierge

Этот документ описывает, как запустить приложение Oskelly Concierge с помощью Docker.

## Требования

- Docker
- Docker Compose

## Быстрый старт

### 1. Запуск всех сервисов с помощью Docker Compose

```bash
# Сборка и запуск всех сервисов
docker-compose up --build

# Запуск в фоновом режиме
docker-compose up -d --build
```

### 2. Запуск только приложения (если у вас уже есть PostgreSQL и Kafka)

```bash
# Сборка Docker образа
docker build -t oskelly-concierge .

# Запуск контейнера
docker run -p 8080:8080 \
  -e DATABASE_HOST=your-postgres-host \
  -e DATABASE_PORT=5432 \
  -e DATABASE_NAME=oskelly-concierge \
  -e DATABASE_USER=oskelly \
  -e DATABASE_PASSWORD=your-password \
  -e KAFKA_BOOTSTRAP_SERVERS=your-kafka-host:9092 \
  oskelly-concierge
```

## Структура сервисов

### PostgreSQL
- **Порт**: 5432
- **База данных**: oskelly-concierge
- **Пользователь**: oskelly
- **Пароль**: qwerty

### Kafka
- **Порт**: 9092
- **Zookeeper порт**: 2181

### Oskelly Concierge Application
- **Порт**: 8080
- **Health Check**: http://localhost:8080/actuator/health
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API Docs**: http://localhost:8080/v3/api-docs

## Переменные окружения

Приложение поддерживает следующие переменные окружения:

### База данных
- `DATABASE_HOST` - хост PostgreSQL (по умолчанию: localhost)
- `DATABASE_PORT` - порт PostgreSQL (по умолчанию: 5432)
- `DATABASE_NAME` - имя базы данных (по умолчанию: oskelly-concierge)
- `DATABASE_USER` - пользователь БД (по умолчанию: oskelly)
- `DATABASE_PASSWORD` - пароль БД (по умолчанию: qwerty)

### Kafka
- `KAFKA_BOOTSTRAP_SERVERS` - серверы Kafka (по умолчанию: localhost:9092)
- `KAFKA_SECURITY_PROTOCOL` - протокол безопасности (по умолчанию: SASL_PLAINTEXT)
- `KAFKA_SASL_MECHANISM` - механизм SASL (по умолчанию: SCRAM-SHA-512)
- `KAFKA_USER` - пользователь Kafka (по умолчанию: oskelly)
- `KAFKA_PASSWORD` - пароль Kafka (по умолчанию: oskelly123)

### Bitrix
- `BITRIX_URL` - URL Bitrix24
- `BITRIX_WEBHOOK` - webhook для Bitrix24
- `BITRIX_USER_ID` - ID пользователя в Bitrix24

### Приложение
- `PORT` - порт приложения (по умолчанию: 8080)
- `SPRING_PROFILES_ACTIVE` - активный профиль Spring (по умолчанию: base)

## Управление сервисами

```bash
# Просмотр логов
docker-compose logs -f oskelly-concierge

# Остановка сервисов
docker-compose down

# Остановка с удалением volumes
docker-compose down -v

# Перезапуск конкретного сервиса
docker-compose restart oskelly-concierge

# Масштабирование (если нужно)
docker-compose up --scale oskelly-concierge=2
```

## Отладка

### Подключение к базе данных
```bash
docker-compose exec postgres psql -U oskelly -d oskelly-concierge
```

### Просмотр логов Kafka
```bash
docker-compose logs -f kafka
```

### Выполнение команд внутри контейнера приложения
```bash
docker-compose exec oskelly-concierge bash
```

## Производственное развертывание

Для производственного развертывания рекомендуется:

1. Использовать внешние сервисы для PostgreSQL и Kafka
2. Настроить переменные окружения через `.env` файл или системные переменные
3. Использовать Docker Swarm или Kubernetes для оркестрации
4. Настроить мониторинг и логирование
5. Использовать HTTPS и настроить безопасность

### Пример .env файла

```env
# Database
DATABASE_HOST=your-postgres-host
DATABASE_PORT=5432
DATABASE_NAME=oskelly-concierge
DATABASE_USER=oskelly
DATABASE_PASSWORD=secure-password

# Kafka
KAFKA_BOOTSTRAP_SERVERS=your-kafka-host:9092
KAFKA_USER=oskelly
KAFKA_PASSWORD=secure-kafka-password

# Bitrix
BITRIX_URL=https://your-bitrix24.domain.com
BITRIX_WEBHOOK=your-webhook-key
BITRIX_USER_ID=your-user-id

# Application
PORT=8080
SPRING_PROFILES_ACTIVE=production
```

## Troubleshooting

### Проблемы с подключением к базе данных
- Убедитесь, что PostgreSQL запущен и доступен
- Проверьте правильность переменных окружения
- Проверьте сетевые настройки Docker

### Проблемы с Kafka
- Убедитесь, что Zookeeper запущен перед Kafka
- Проверьте настройки SASL, если используется аутентификация
- Проверьте доступность портов

### Проблемы с приложением
- Проверьте логи: `docker-compose logs oskelly-concierge`
- Убедитесь, что все зависимости (БД, Kafka) доступны
- Проверьте health endpoint: http://localhost:8080/actuator/health