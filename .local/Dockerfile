FROM maven:3.9.9-eclipse-temurin-21 AS builder

WORKDIR /build

COPY . .

# Собираем JAR внутри контейнера
RUN mvn clean package -DskipTests

# Копируем logback.xml и JAR в нужное место (уже внутри контейнера)
COPY src/main/resources/logback.xml /build/logback.xml
# JAR уже в target/, копируем его
COPY target/concierge-0.0.1-SNAPSHOT.jar /build/concierge.jar

#RUN mvn clean package -DskipTests
#ADD src/main/resources/logback.xml /build/logback.xml
#ADD target/concierge-0.0.1-SNAPSHOT.jar /build/concierge.jar

ENV JAVA_TOOL_OPTIONS="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006"

#ENV DATABASE_HOST=localhost
# -- PMP --
ENV DATABASE_HOST=***********
#ENV DATABASE_PORT=5020
ENV DATABASE_PORT=5021
ENV DATABASE_NAME=oskelly-conceirge
ENV DATABASE_USER=oskelly
ENV DATABASE_PASSWORD=qwerty
# -- PMP --
ENV PORT=8080
# -----
ENV KAFKA_BOOTSTRAP_SERVERS=localhost:9092
ENV KAFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT
ENV KAFKA_SASL_MECHANISM=SCRAM-SHA-512
ENV KAFKA_USER=oskelly
ENV KAFKA_PASSWORD=oskelly123
ENV KAFKA_SASL_JAAS_CONFIG="org.apache.kafka.common.security.scram.ScramLoginModule required username=\"${KAFKA_USER}\" password=\"${KAFKA_PASSWORD}\";"
ENV KAFKA_CONCIERGE_GROUP_ID=oskelly-concierge
ENV SWAGGER_CONFIG_URL=/v3/api-docs/swagger-config
ENV SWAGGER_URL=/v3/api-docs
ENV MONOLITH_HOSTNAME=https://oskelly.ru
ENV BITRIX_URL=https://dev-bitrix24.oskelly.tech
ENV BITRIX_WEBHOOK=w7z3svls1ovbiha3
ENV BITRIX_USER_ID=28486
ENV KAFKA_CONCIERGE_PHONE_VERIFICATION_TOPIC=phone-verification-events
ENV KAFKA_CONCIERGE_PHONE_VERIFICATION_TOPIC_ENABLE=true
ENV KAFKA_CONCIERGE_SALES_REQUESTS_AVAILABLE_TOPIC=sales-requests-available
ENV KAFKA_CONCIERGE_SALES_REQUESTS_AVAILABLE_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_SALES_AVAILABLE_TOPIC=sales-available
ENV KAFKA_CONCIERGE_SALES_AVAILABLE_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_SALES_ASSIGNMENTS_CLIENT_TOPIC=sales-assignments-client
ENV KAFKA_CONCIERGE_SALES_ASSIGNMENTS_CLIENT_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_SALES_REQUESTS_REASSIGNMENT_ORDERS_TOPIC=sales-requests-reassignment-orders
ENV KAFKA_CONCIERGE_SALES_REQUESTS_REASSIGNMENT_ORDERS_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_SOURCER_REQUESTS_AVAILABLE_TOPIC=sourcer-requests-available
ENV KAFKA_CONCIERGE_SOURCER_REQUESTS_AVAILABLE_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_SOURCER_AVAILABLE_TOPIC=sourcer-available
ENV KAFKA_CONCIERGE_SOURCER_AVAILABLE_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_SOURCER_REQUESTS_REASSIGNMENT_ORDERS_TOPIC=sourcer-requests-reassignment-orders
ENV KAFKA_CONCIERGE_SOURCER_REQUESTS_REASSIGNMENT_ORDERS_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_TOPIC=offer-message-client-send-request
ENV KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_TOPIC_ENABLED=true
ENV KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_RESULT_TOPIC=offer-message-client-send-request-result
ENV KAFKA_CONCIERGE_OFFER_MESSAGE_CLIENT_SEND_REQUEST_RESULT_TOPIC_ENABLED=true

EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/build/target/concierge-0.0.1-SNAPSHOT.jar", "-Xmx2064m", "--spring.profiles.active=base", "-Dlogging.config=/build/logback.xml"]