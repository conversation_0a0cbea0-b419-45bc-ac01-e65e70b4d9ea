# oskelly-concierge

## Common information

This service is for saving and serving purchase orders from customers
Containing all necessary information about purchase orders, BPMN Driver and API 

## Заявки на покупку

### Процесс назначения Сейлза на заявку

Процесс назначения Сейлза на заявку состоит из двух этапов и начинается при переходе заявки в статус NEW.

#### Этап 1

При переходе заявки в статус NEW в топик `sales.requests.available` отправляется запрос на получение свободного Сейлза из очереди.

> Микросервис sales-app-api обеспечивает получение Сейлзов в порядке очереди, у которых на данный момент в графике работ
стоит рабочий день. Если свободного Сейлза нет, то заявка назначается на Админа Сейлзов.

#### Этап 2

Из топика `sales.available` производится чтение результатов запроса свободного Сейлза, после чего полученный Сейлз
назначается на указанную заявку.

В сервис `sales-app-api` через топик `sales.assignments.client` отправляется запрос на назначение клиента Сейлзу, 
взявшему заявку (в случае, если на клиента не назначен сотрудник).

### Процесс перераспределения заявок Сейлза в случае изменения сотрудника

В процессе работы у Сейлза:

- может измениться роль;
- сотрудник может быть удален;
- рабочий день в графике работ может быть изменен на выходной.

При возникновении подобных событий в Kafka отправляется запрос на перераспределение заявок Сейлза в топик
`sales-requests-reassignment-orders-topic`.

После чтения сообщения происходит процесс перераспределения заявок указанного Сейлза.

> При изменении Сейлза заявки распределяются на его Админа. При изменении Админа заявки распределяются на свободных
> Сейлзов в порядке очереди.

### Процесс назначения Сорсера на заявку

Процесс назначения Сорсера на заявку состоит из двух этапов и начинается при переходе заявки в статус AWAITING_SOURCER.

#### Этап 1

При переходе заявки в статус AWAITING_SOURCER в топик `sourcer.requests.available` отправляется запрос на получение
свободного Сорсера из очереди.

> Микросервис sales-app-api обеспечивает получение Сорсеров в порядке очереди, у которых на данный момент в графике работ
стоит рабочий день. Если свободного Сейлза нет, то заявка назначается на Админа Сорсеров.

#### Этап 2

Из топика `sourcer.available` производится чтение результатов запроса свободного Сорсера, после чего полученный Сорсер
назначается на указанную заявку.

### Процесс перераспределения заявок Сорсера в случае изменения сотрудника

В процессе работы у Сорсера:

- может измениться роль;
- сотрудник может быть удален;
- рабочий день в графике работ может быть изменен на выходной.

При возникновении подобных событий в Kafka отправляется запрос на перераспределение заявок Сорсера в топик
`sourcer-requests-reassignment-orders-topic`.

После чтения сообщения происходит процесс перераспределения заявок указанного Сорсера.

> При изменении Сорсера заявки распределяются на его Админа. При изменении Админа заявки распределяются на свободных
> Сорсеров в порядке очереди.

### Ручное назначение заявок на Сейлза и Сорсера

Для служебных целей предоставляются методы ручного назначения заявок.

#### Назначение заявки на Сейлза

```http request
POST http://<host>/api/v1/purchase/order/{orderId}/assign/sales
```

#### Назначение заявки на Сорсера

```http request
POST http://<host>/api/v1/purchase/order/{orderId}/assign/sourcer
```
#### Тесты в CI